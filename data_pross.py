import time
import scipy.spatial as spspatial
import deepxde as dde
import matplotlib.pyplot as plt
import numpy as np
import os
import re

dde.backend.set_default_backend('pytorch')
dde.config.disable_xla_jit()

PATH = "/gpfs/gibbs/project/lu_lu/wj259/RTEDeepONet/McCaffreyAll/"
INPUT_PATH = "/gpfs/gibbs/project/lu_lu/wj259/RTEDeepONet/data/"

def parse_filename(filename):
    """Parse filename to extract start and end time values"""
    # Updated pattern to match firefoam_data_X_START_END.npz format
    pattern = re.compile(r'firefoam_data_\d+_(\d+\.\d+)_(\d+\.\d+)\.npz')
    match = pattern.match(filename)
    if match:
        # Start time is group 1, end time is group 2
        start_val = float(match.group(1))
        end_val = float(match.group(2))
        return start_val, end_val
    return None, None


def extract_data(filename):
    nsk = nst = 272580
    nPhi_ = 2
    nTheta_ = 2
    ns = nTheta_*nPhi_*4
    nx = 272580

    data = np.load(f"{INPUT_PATH}{filename}", allow_pickle=True)
    X, Y, Z = np.loadtxt("/gpfs/gibbs/project/lu_lu/wj259/RTEDeepONet/meshCellCenters.txt", usecols=(0,1,2), unpack=True)

    numeric_keys = [str(k) for k in data["numeric_keys"].tolist()]
    numeric_keys.sort(key=lambda s: float(str(s)))

    valid_keys = [
        key for key in map(str, numeric_keys)
        if all([f"T_{key}" in data, f"G_{key}" in data, f"kappa_{key}" in data, f"I_{key}" in data])
    ]

    nk = len(valid_keys)
    print(len(numeric_keys) - nk, "invalid key(s) ...")

    I_all = np.zeros((nk, nx, ns), dtype=np.float32)
    for i, k in enumerate(valid_keys):
        I_raw = data[f"I_{k}"]
        I_all[i] = I_raw.T if I_raw.shape[0] == 16 else I_raw

    T_all = np.array([data[f"T_{k}"] for k in valid_keys], dtype=np.float32)
    G_all = np.array([data[f"G_{k}"] for k in valid_keys], dtype=np.float32)
    kappa_all = np.array([data[f"kappa_{k}"] for k in valid_keys], dtype=np.float32)

    print(f"\nLoaded data shapes:")
    print(f"{nk} samples -- T_all: {T_all.shape}, kappa_all: {kappa_all.shape}, I_all: {I_all.shape}, G_all: {G_all.shape}")

    deltaPhi = np.pi/(2.0*nPhi_)
    deltaTheta = np.pi/nTheta_
    ts = [(2*n + 1)*deltaTheta/2.0 for n in range(nTheta_)]
    ps = [(2*m + 1)*deltaPhi/2.0 for m in range(4*nPhi_)]
    ps, ts = np.meshgrid(ps, ts)
    ps, ts = ps.flatten(), ts.flatten()

    X_trunk = np.hstack((
        np.tile(X.reshape((-1, 1)), (ns, 1)),
        np.tile(Y.reshape((-1, 1)), (ns, 1)),
        np.tile(Z.reshape((-1, 1)), (ns, 1)),
        np.repeat(ps, nx).reshape((-1, 1)),
        np.repeat(ts, nx).reshape((-1, 1))
    )).astype(np.float32)

    print(f"X_trunk shape: {X_trunk.shape}")
    print(f"First 5 trunk points (with angles):\n{X_trunk[:5,:]}")
    X_full, Y_full, Z_full = X_trunk[:,0], X_trunk[:,1], X_trunk[:,2]
    return kappa_all, T_all, I_all, G_all, X_trunk, X_full, Y_full, Z_full, nk, numeric_keys

def plot_2d(grid_x, grid_y, grid_z, c_val, i=0.05):
    y_slice = np.isclose(grid_y.flatten(), 0.05)
    plt.figure(figsize=(5,5))
    plt.scatter(grid_x.flatten()[y_slice], grid_z.flatten()[y_slice], c=c_val[y_slice], cmap = "inferno")
    plt.xlabel("$x$")
    plt.ylabel("$z$")
    plt.title(f"2D Plot for y={i} Slice")
    plt.savefig(f"2D_plot.png")

def interp_weights(xyz, uvw, d=3):
    # triangulate the irregular grid coordinates
    # breaks down the space into simplices
    tri = spspatial.Delaunay(xyz)
    # Each point in uvw is located within or outside these simplices
    simplex = tri.find_simplex(uvw)
    # Retrieves vertices of the simplices and transformation matrices
    # that map simplex coordinates to reference simplex coordinates.
    vertices = np.take(tri.simplices, simplex, axis=0)
    temp = np.take(tri.transform, simplex, axis=0)
    delta = uvw - temp[:, d]
    # Computes the barycentric coordinates of the points uvw with respect to the simplices
    bary = np.einsum('njk,nk->nj', temp[:, :d, :], delta)
    extra_idx = simplex == -1
    weights = np.hstack((bary, 1 - bary.sum(axis=1, keepdims=True)))
    weights[extra_idx] = np.nan
    return vertices, weights

def interp_linear(X, Y, Z, grid_x, grid_y, grid_z, mask):
    nx = 272580
    points = np.vstack((X[:nx][mask[:nx]], Y[:nx][mask[:nx]], Z[:nx][mask[:nx]])).T
    new_points = np.vstack((grid_x.flatten(), grid_y.flatten(), grid_z.flatten())).T
    print("points.shape, new_points.shape: ", points.shape, new_points.shape)
    vtx, wts = interp_weights(points, new_points)
    return vtx, wts, points, new_points

def nearest_neighbor_interp(xyz, uvw):
    # Build a KDTree for quick nearest-neighbor lookup
    tree = spspatial.KDTree(xyz)
    # Query the KDTree for the nearest neighbor of each point in uvw
    dist, indices = tree.query(uvw)
    return indices

def interp_values(vtx, wts, values, mask, neighbor_inds):
    nx = values.shape[0]
    values = values[mask[:nx]]
    # print(values.shape)
    ret = np.einsum('nj,nj->n', np.take(values, vtx), wts)
    nan_inds = np.any(np.isnan(wts) | (wts < 0), axis=1)
    # print(ret.shape, nan_inds)
    fill_value = values[neighbor_inds[nan_inds]]#nearest_neighbor_interp(points, new_points[nan_inds, :], values)
    ret[nan_inds] = fill_value
    grid_values = ret
    return grid_values

def prepare_interpolation(X, Y, Z, level = "four"):
    lfour_mask = (X >= -1.5) & (X <= 1.5) & (Y >= -1.5) & (Y <= 1.5) & (Z >= 0) & (Z <= 3)
    lthree_mask = (X >= -0.6) & (X <= 0.6) & (Y >= -0.6) & (Y <= 0.6) & (Z >= 0) & (Z <= 2)
    ltwo_mask = (X >= -0.5) & (X <= 0.5) & (Y >= -0.5) & (Y <= 0.5) & (Z >= 0) & (Z <= 1)
    # nx4, nx3, nx2, nx1 = int(lthree_mask.shape[0]/16), int(sum(lthree_mask)/16), int(sum(ltwo_mask)/16), int(sum(lone_mask)/16)
    t0 = time.time()
    if level == "four":
        mask_intp = lfour_mask
        grid_x, grid_y, grid_z = np.mgrid[-1.45:1.45:30j, -1.45:1.45:30j, 0.05:2.95:30j]
    elif level == "three":
        mask_intp = lthree_mask
        grid_x, grid_y, grid_z = np.mgrid[-0.575:0.575:24j, -0.575:0.575:24j, 0.025:1.975:40j]
    elif level == "two":
        mask_intp = ltwo_mask
        grid_x, grid_y, grid_z = np.mgrid[-0.4875:0.4875:40j, -0.4875:0.4875:40j, 0.0125:0.9875:40j]
    vtx, wts, points, new_points = interp_linear(X, Y, Z, grid_x, grid_y, grid_z, mask_intp)
    print("The first step took ", time.time() - t0, " s.")
    t01 = time.time()
    neighbor_inds = nearest_neighbor_interp(points, new_points)
    print("The second step took ", time.time() - t01, " s.")
    return vtx, wts, neighbor_inds, mask_intp

def interpolate_and_save_data(X_branch1, X_branch2, Is, Gs, vtx, wts, neighbor_inds, mask_intp, start, end, numeric_keys, level="four"):
    ns = 16
    if level == "four":
        npoints = 30**3
        grid_x, grid_y, grid_z = np.mgrid[-1.45:1.45:30j, -1.45:1.45:30j, 0.05:2.95:30j]
    elif level == "three":
        npoints = 24*24*40
        grid_x, grid_y, grid_z = np.mgrid[-0.575:0.575:24j, -0.575:0.575:24j, 0.025:1.975:40j]
    elif level == "two":
        npoints = 40**3
        grid_x, grid_y, grid_z = np.mgrid[-0.4875:0.4875:40j, -0.4875:0.4875:40j, 0.0125:0.9875:40j]
    X_branch1_intp = np.zeros((len(X_branch1),npoints))
    X_branch2_intp = np.zeros((len(X_branch2),npoints))
    G_intp = np.zeros((len(Gs), npoints))
    for i in range(len(X_branch1)):
        X_branch1_intp[i] = interp_values(vtx, wts, X_branch1[i], mask_intp, neighbor_inds)
        X_branch2_intp[i] = interp_values(vtx, wts, X_branch2[i], mask_intp, neighbor_inds)
        G_intp[i] = interp_values(vtx, wts, Gs[i], mask_intp, neighbor_inds)

    Is_intp = np.zeros((len(X_branch2), ns*npoints))
    for i in range(len(X_branch1)):
        for j in range(ns):
            Is_intp[i, j*npoints:(j+1)*npoints] = interp_values(vtx, wts, Is[i, :, j], mask_intp, neighbor_inds)

    nPhi_ = 2
    nTheta_ = 2
    deltaPhi = np.pi/(2.0*nPhi_)
    deltaTheta = np.pi/nTheta_
    ts = [(2*n + 1)*deltaTheta/2.0 for n in range(nTheta_)]
    ps = [(2*m + 1)*deltaPhi/2.0 for m in range(4*nPhi_)]
    ps, ts = np.meshgrid(ps, ts)
    ps, ts = ps.flatten(), ts.flatten()

    # np.tile(grid_x.reshape((-1,1)), (ns, 1)), np.tile(grid_y.reshape((-1,1)), (ns, 1)), np.tile(grid_z.reshape((-1,1)), (ns, 1)),
    X_trunk_intp = np.column_stack((ps, ts))

    # Convert start and end to integers for cleaner filenames
    start_int = int(start)
    end_int = int(end)

    print(f"\nX_trunk_intp shape: {X_trunk_intp.shape}")
    np.savez_compressed(f"{PATH}McCaffreyAll_{level}_{start_int}_{end_int}.npz",
                       X_branch1=X_branch1_intp,
                       X_branch2=X_branch2_intp,
                       X_trunk=X_trunk_intp,
                       Is=Is_intp,
                       Gs=G_intp,
                       numeric_keys=numeric_keys)
    print(f"Saved interpolated data for {level} level, and start={start_int}, end={end_int}.\n")
    # plot_2d(X_trunk_intp[:npoints,0], X_trunk_intp[:npoints,1], X_trunk_intp[:npoints,2], Is_intp[0, 3*npoints:4*npoints])
    # plot_2d(X_trunk_intp[:npoints,0], X_trunk_intp[:npoints,1], X_trunk_intp[:npoints,2], X_branch2_intp[0, :npoints])

def get_level_one_data(kappas, Ts, Is, Gs, X, Y, Z, start, end, numeric_keys):
    lone_mask = (X >= -0.3) & (X <= 0.3) & (Y >= -0.3) & (Y <= 0.3) & (Z >= 0) & (Z <= 1)
    npoints = 48*48*80
    ns = 16
    nx = kappas.shape[1]
    X_branch1 = kappas[:,lone_mask[:nx]]
    X_branch2 = Ts[:,lone_mask[:nx]]
    G = Gs[:,lone_mask[:nx]]
    Is_intp = np.zeros((len(X_branch1), ns*npoints))
    for j in range(ns):
        Is_intp[:, j*npoints:(j+1)*npoints] = Is[:,lone_mask[:nx], j]

    nPhi_ = 2
    nTheta_ = 2
    deltaPhi = np.pi/(2.0*nPhi_)
    deltaTheta = np.pi/nTheta_
    ts = [(2*n + 1)*deltaTheta/2.0 for n in range(nTheta_)]
    ps = [(2*m + 1)*deltaPhi/2.0 for m in range(4*nPhi_)]
    ps, ts = np.meshgrid(ps, ts)
    ps, ts = ps.flatten(), ts.flatten()

    # np.tile(grid_x.reshape((-1,1)), (ns, 1)), np.tile(grid_y.reshape((-1,1)), (ns, 1)), np.tile(grid_z.reshape((-1,1)), (ns, 1)),
    X_trunk_intp = np.column_stack((ps, ts))

    print(f"\nX_trunk_intp shape: {X_trunk_intp.shape}")
    np.savez_compressed(f"{PATH}McCaffreyAll_one_{start}_{end}.npz",
                       X_branch1=X_branch1,
                       X_branch2=X_branch2,
                       X_trunk=X_trunk_intp,
                       Is=Is_intp,
                       Gs=G,
                       numeric_keys=numeric_keys)
    print(f"Saved interpolated data for one level, and start={start}, end={end}.\n")


def main():
    os.makedirs(PATH, exist_ok=True)

    # Get all input files and print them for debugging
    all_files = [f for f in os.listdir(INPUT_PATH) if f.endswith('.npz')]
    print(f"All .npz files in {INPUT_PATH}:")
    for f in all_files:
        print(f"  - {f}")

    # Filter files where start time is less than or equal to 60
    # and sort by the actual start time value
    input_files = []
    for filename in all_files:
        start, end = parse_filename(filename)
        print(f"Parsing {filename}: start={start}, end={end}")
        if start is not None and start <= 60:
            input_files.append((filename, start))

    # Sort by start time value
    input_files.sort(key=lambda x: x[1])
    input_files = [f[0] for f in input_files]

    print(f"Found {len(input_files)} files with start time <= 60")
    if len(input_files) > 0:
        print("Files will be processed in this order:")
        for f in input_files:
            start, end = parse_filename(f)
            print(f"  - {f} (start time: {start}, end time: {end})")
    else:
        print("No files to process. Check your file naming pattern and INPUT_PATH.")
        print(f"Current directory: {os.getcwd()}")
        print(f"INPUT_PATH: {INPUT_PATH}")
        print(f"Absolute INPUT_PATH: {os.path.abspath(INPUT_PATH)}")

    for filename in input_files:
        # Parse filename to get start and end values
        start, end = parse_filename(filename)
        if start is None or end is None:
            print(f"Could not parse filename: {filename}, skipping")
            continue

        print(f"\nProcessing file: {filename} -> McCaffreyAll_*_{start}_{end}.npz")

        # Extract data
        try:
            kappas, Ts, Is, Gs, X_trunk, X, Y, Z, nk, numeric_keys = extract_data(filename)

            # Process different levels of data
            for level in ["four", "three", "two"]:
                print(f"\nProcessing {level} level data...")
                vtx, wts, neighbor_inds, mask_intp = prepare_interpolation(X, Y, Z, level=level)
                interpolate_and_save_data(kappas, Ts, Is, Gs, vtx, wts, neighbor_inds, mask_intp, start, end, numeric_keys, level=level)

            # Process level one data
            print("\nProcessing level one data...")
            get_level_one_data(kappas, Ts, Is, Gs, X, Y, Z, start, end, numeric_keys)
        except Exception as e:
            print(f"Error processing file {filename}: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
