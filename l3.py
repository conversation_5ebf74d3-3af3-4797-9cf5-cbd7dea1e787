import time
import os
import scipy
import torch
from torch.cuda.amp import autocast
from torch.autograd import profiler
import argparse
import deepxde as dde
import matplotlib.pyplot as plt
import numpy as np
from model_55_60 import *
dde.backend.set_default_backend('pytorch')
dde.config.disable_xla_jit()
import sys
import logging
from typing import *
from datetime import datetime
from sklearn.model_selection import train_test_split
sys.path.append(os.path.abspath(os.path.join(__file__, "../../../src")))
from RTEDeepONet import McCaffrey_fire

PATH_L3 = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/data_lthree/"
PATH_L4 = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/data_lfour/"
MODEL_PATH = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/model"
LOG_PATH = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/logs"
IS_WITHX = True

class Logger(object):
    def __init__(self, filename="Default.log"):
        self.terminal = sys.stdout
        self.log = open(filename, "a")

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)

    def flush(self):
        self.terminal.flush()
        self.log.flush()
        
    def isatty(self):
        # pretend we're never a TTY; or you could return self.terminal.isatty()
        return False

    def close(self):
        sys.stdout = self.terminal
        self.log.close()


def load_level3_and_level4(test_ratio = 100/3999):
    data_files = [
        "lthree_data_54.7507.npz",
        "lthree_data_58.6201.npz",
        # "lthree_data_62.5324.npz",
        "lthree_data_66.34.npz",
        "lthree_data_70.1529.npz",
        "lthree_data_73.9086.npz"
    ]

    test_file = "lthree_data_58.6201.npz"
    train_files = [f for f in data_files]

    print("\n===== [DEBUG] Data Split =====")
    print("Training files:")
    for f in train_files:
        print(f"  - {f}  |  ↔  {f.replace('lthree', 'lfour')}")
    print("Testing file:")
    print(f"  - {test_file}  |  ↔  {test_file.replace('lthree', 'lfour')}")
    print("=====================================\n")

    # Load first file to get X_trunk
    first_data = np.load(os.path.join(PATH_L3, data_files[0]))
    X_trunk = first_data["X_trunk"]

    # Load training data
    X_branch1_list, X_branch2_list, I_l3_list, I_l4_list = [], [], [], []

    for i, file in enumerate(train_files):
        d3 = np.load(os.path.join(PATH_L3, file))
        d4 = np.load(os.path.join(PATH_L4, file.replace("lthree", "lfour")))
        X_branch1_list.append(d3["X_branch1"])
        X_branch2_list.append(d3["X_branch2"])
        I_l3_list.append(d3["I"])
        I_l4_list.append(d4["I"])
        print(f"Loaded training file {i}: {file}, shapes:", 
              d3["X_branch1"].shape, d3["X_branch2"].shape, d3["I"].shape, d4["I"].shape)

    # Combine training data
    X1_train = np.vstack(X_branch1_list)
    X2_train = np.vstack(X_branch2_list)
    I_l3_train = np.vstack(I_l3_list)
    I_l4_train = np.vstack(I_l4_list)
    print("Combined training data shapes:", X1_train.shape, X2_train.shape, I_l3_train.shape, I_l4_train.shape)

    # Load test data
    d3_test = np.load(os.path.join(PATH_L3, test_file))
    d4_test = np.load(os.path.join(PATH_L4, test_file.replace("lthree", "lfour")))
    X1_test = d3_test["X_branch1"]
    X2_test = d3_test["X_branch2"]
    I_l3_test = d3_test["I"]
    I_l4_test = d4_test["I"]
    print(f"Loaded test file: {test_file}, shapes:", X1_test.shape, X2_test.shape, I_l3_test.shape, I_l4_test.shape)

    # Prepare data for model
    Xs_train = (X1_train.astype(np.float32, copy=False), 
                X2_train.astype(np.float32, copy=False), 
                X_trunk.astype(np.float32, copy=False))
    Is_l3_train = I_l3_train.astype(np.float32, copy=False)
    Is_l4_train = I_l4_train.astype(np.float32, copy=False)

    Xs_test = (X1_test.astype(np.float32, copy=False), 
              X2_test.astype(np.float32, copy=False), 
              X_trunk.astype(np.float32, copy=False))
    Is_l3_test = I_l3_test.astype(np.float32, copy=False)
    Is_l4_test = I_l4_test.astype(np.float32, copy=False)

    # Preprocess data
    Xs_train, Is_l3_train, Xs_test, Is_l3_test, T_scaler, k_scaler, scaler = McCaffrey_fire.preprocess_data(
        len(X1_train), 20, Xs_train, Is_l3_train, Xs_test, Is_l3_test, isPCA=False, n_pca=0
    )

    # Scale level 4 data with the same scaler
    Is_l4_train = Is_l4_train / scaler
    Is_l4_test = Is_l4_test / scaler

    # Use a subset for quick testing
    Xs_test_subset = (Xs_test[0][:20], Xs_test[1][:20], Xs_test[-1])
    Is_l3_test_subset = Is_l3_test[:20]
    Is_l4_test_subset = Is_l4_test[:20]

    print(Xs_train[1].shape, Xs_train[-1].shape, Xs_test_subset[1].shape, 
          Is_l3_train.shape, Is_l3_test_subset.shape, Is_l4_train.shape, Is_l4_test_subset.shape)
    
    return Xs_train, Is_l3_train, Is_l4_train, Xs_test_subset, Is_l3_test_subset, Is_l4_test_subset, scaler


def deeponet_3d_fourier_nested3(X_train, I_train, X_test, I_test,
                     isRestore=False, isKAN=False,
                     model_restore_path = f"{MODEL_PATH}/modeltest.pt",
                     model_save_path = f"{MODEL_PATH}/modeltest",
                     output_dir = f"{LOG_PATH}/history",
                     activation = ["relu", "relu", "gelu"],
                     batch_b = 2**3,
                     batch_t = 151**2,
                     n = 128,
                     modes1=15, modes2=15, modes3=10, nblocks=4,
                     iterations = 1000,
                     optimizer ="adam",
                     withbc = False):

    nsk = np.prod(X_train[0].shape[1:])
    nst = np.prod(X_train[1].shape[1:])
    dim_x = X_train[-1].shape[-1]
    print("dim_x:", nsk, nst, dim_x)
    print("Activation functions: ", activation)

    for i in range(len(activation)):
        if activation[i] == "gelu":
            activation[i] = torch.nn.GELU()
        elif activation[i] == "swish":
            activation[i] = torch.nn.SiLU()
        elif activation[i] == "relu":
            activation[i] = torch.nn.ReLU()

    data = QuadrupleCartesianProd(X_train, I_train, X_test, I_test)
    print(data.train_x[0].shape, data.train_x[1].shape, data.train_x[-1].shape, data.train_y.shape, data.test_y.shape)

    X_trunk_layers = [dim_x, n, n, n]
    

    if IS_WITHX:
        branch = branch_withx
    X_branch1_layers = [nsk, branch(n)]
    X_branch2_layers = [nst, branch(n)]

    output_merger_layers = [n, decoder(modes1, modes2, modes3, n, nblocks)]

    net = MIONetCartesianProd(
            X_branch1_layers,
            X_branch2_layers,
            X_trunk_layers,
            {"branch1": activation[0], "branch2": activation[1], "trunk": activation[2], "output merger": activation[2]},
            "Glorot normal",
            regularization=["l2", 1e-6],
            merge_operation="sum",      # use add instead of mul
            trunk_last_activation=True,
            layer_sizes_merger=None,
            output_merge_operation="mul",
            layer_sizes_output_merger=output_merger_layers,
        )

    def output_transform(inputs, outputs):
         return outputs**2

    net.apply_output_transform(output_transform)

    learning_rate = 1e-3
    if optimizer=="adam":
        optimizer = torch.optim.Adam(net.parameters())
    model = dde.Model(data, net)
    
    if isRestore:
        model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), loss="mse", metrics=["l2 relative error"])
        model.restore(model_restore_path)
        print("Model restored from: ", model_restore_path)
    else:
        print("Batch size of branch and trunk:", (batch_b, batch_t), ", Optimizer:", optimizer, ", #Iterations:", iterations)
        model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), metrics=["l2 relative error"])
        checkpointer = dde.callbacks.ModelCheckpoint(model_save_path, save_better_only=True, period=2000)
        losshistory, train_state = model.train(iterations=iterations, batch_size=batch_b, display_every=500, callbacks=[checkpointer], model_save_path=model_save_path)
        dde.saveplot(losshistory, train_state, issave=True, isplot=False, output_dir=output_dir)
    
    print("# Parameters:", net.num_trainable_parameters())

    return model


def main(level, width, iterations, batch_b, batch_t, modes1, modes2, modes3, nblocks):
    Xs_train, Is_train, Is_train_four, Xs_test, Is_test, Is_test_four, scaler = load_level3_and_level4()

    train_shape = (len(Is_train), 24, 24, 40) 
    test_shape = (len(Is_test), 24, 24, 40)   
    train_shape_four = (len(Is_train), 16, 30, 30, 30)
    test_shape_four = (len(Is_test), 16, 30, 30, 30)
    test_shape0 = (len(Is_test_four), 16, 30, 30, 30)

    unique_ps, unique_ts = np.meshgrid(np.unique(Xs_train[-1][:, 3]), np.unique(Xs_train[-1][:, 4]))
    X_trunk_array = np.column_stack((unique_ps.flatten(), unique_ts.flatten())).astype(np.float32)

    Is_train_four = np.transpose(Is_train_four.reshape(train_shape_four), (0, 2, 3, 4, 1))
    Is_test_four = np.transpose(Is_test_four.reshape(test_shape_four), (0, 2, 3, 4, 1))

    if IS_WITHX:
        grid_x, grid_y, grid_z = np.mgrid[-1.45:1.45:24j, -1.45:1.45:24j, 0.05:2.95:40j]
        new_points = np.vstack((grid_x.flatten(), grid_y.flatten(), grid_z.flatten())).T.reshape(24, 24, 40, 3)
        new_points_train = np.repeat(new_points[None, ...], len(Is_train), axis=0).astype(np.float32)
        new_points_test = np.repeat(new_points[None, ...], len(Is_test), axis=0).astype(np.float32)
        new_points_test0 = np.repeat(new_points[None, ...], len(Is_test_four), axis=0).astype(np.float32)

        # 修正 reshape 和 concatenate 操作
        X_train = (
            np.concatenate((new_points_train, np.expand_dims(Xs_train[0].reshape(train_shape), -1)), axis=-1),
            np.concatenate((new_points_train, np.expand_dims(Xs_train[1].reshape(train_shape), -1)), axis=-1),
            X_trunk_array
        )
        X_test = (
            np.concatenate((new_points_test, np.expand_dims(Xs_test[0].reshape(test_shape), -1)), axis=-1),
            np.concatenate((new_points_test, np.expand_dims(Xs_test[1].reshape(test_shape), -1)), axis=-1),
            X_trunk_array
        )
        X_test0 = (
            Is_test_four.astype(np.float32),
            np.stack((Xs_test[0], Xs_test[1]), axis=-1).reshape(test_shape + (2,)).astype(np.float32),
            X_trunk_array
        )
        print(X_train[0].shape, X_train[-1].shape, X_test[1].shape, Is_train.shape, Is_test.shape)

    else:
    
        X_train = (
            Xs_train[0].reshape(train_shape),
            Xs_train[1].reshape(train_shape),
            X_trunk_array
        )
        X_test = (
            Xs_test[0].reshape(test_shape),
            Xs_test[1].reshape(test_shape),
            X_trunk_array
        )
        X_test0 = (
            Is_test_four.astype(np.float32),
            np.stack((Xs_test[0], Xs_test[1]), axis=-1).reshape(test_shape + (2,)).astype(np.float32),
            X_trunk_array
        )
        print("No x:", X_train[0].shape, X_train[-1].shape, X_test[1].shape, Is_train.shape, Is_test.shape)

    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    logfile = f"{LOG_PATH}/log_lthree_wfour_{current_time}_{len(Is_train)}_{width}_{modes1}_{modes2}_{modes3}_{nblocks}_{iterations}.log"
    logger = Logger(logfile)
    sys.stdout = logger
 

    model = deeponet_3d_fourier_nested3(
        X_train, Is_train, X_test, Is_test,
        isRestore=False, isKAN=False,
        model_restore_path = f"{MODEL_PATH}/model_lthree_wfour.pt",
        model_save_path = f"{MODEL_PATH}/model_lthree_wfour",
        output_dir = f"{LOG_PATH}/history_lthree_wfour",
        activation = ["relu", "relu", "swish"],
        batch_b = batch_b,
        batch_t = batch_t,
        n = width,
        modes1 = modes1, modes2 = modes2, modes3 = modes3,
        nblocks = nblocks,
        iterations = iterations,
        optimizer = "adam"
    )
    
    # Inference timing and evaluation
    mixed_precision = False
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    net = model.net
    net.to(device).eval()
    if mixed_precision:
        net.half()

    dtype = torch.float16 if mixed_precision else torch.float32
    X0_all = torch.from_numpy(X_test[0]).to(device, dtype=dtype, non_blocking=True)
    X1_all = torch.from_numpy(X_test[1]).to(device, dtype=dtype, non_blocking=True)
    X2_all = torch.from_numpy(X_test[2]).to(device, dtype=dtype, non_blocking=True)

    # warm-up
    with torch.no_grad():
        for _ in range(5):
            if mixed_precision:
                with autocast():
                    _ = net((X0_all[:1], X1_all[:1], X2_all))
            else:
                _ = net((X0_all[:1], X1_all[:1], X2_all))

    # Inference timing
    nb = 1
    torch.cuda.synchronize()
    t0 = time.time()
    with torch.no_grad():
        for i in range(10):
            if mixed_precision:
                with autocast():
                    _ = net((X0_all[i:i+nb], X1_all[i:i+nb], X2_all))
            else:
                _ = net((X0_all[i:i+nb], X1_all[i:i+nb], X2_all))
    torch.cuda.synchronize()
    print(f"Inference time (batch={nb}): {(time.time() - t0)/10:.4f}s")

    # Calculate errors
    pred_test = []
    test_errors = []
    nb = 1
    nx = 24*24*40
    with torch.no_grad():
        for i in range(len(Is_test)):
            if mixed_precision:
                with autocast():
                    predictions = model.predict((X_test[0][nb*i:nb*(i+1)], X_test[1][nb*i:nb*(i+1)], X_test[2]))
            else:
                predictions = model.predict((X_test[0][nb*i:nb*(i+1)], X_test[1][nb*i:nb*(i+1)], X_test[2]))
            err = dde.metrics.mean_l2_relative_error(scaler*Is_test[nb*i:nb*(i+1)], scaler*predictions)
            test_errors.append(err)
            pred_test.append(predictions)
        print("Mean l2 relative error of testing dataset", np.mean(test_errors))

    # Profile model inference
    i0 = 0
    if mixed_precision:
        with torch.no_grad(), profiler.profile(record_shapes=True, use_cuda=True) as prof:
            with profiler.record_function("model_inference"), autocast():
                out = net((X0_all[i0:i0+nb], X1_all[i0:i0+nb], X2_all))
        print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=15))
    else:
        with torch.no_grad(), profiler.profile(record_shapes=True, use_cuda=True) as prof:
            with profiler.record_function("model_inference"):
                out = net((X0_all[i0:i0+nb], X1_all[i0:i0+nb], X2_all))
        print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=15))

    from RTEDeepONet.ssim3d import ssim3D
    ssims = []
    nb = 1
    nx = 24*24*40  
    with torch.no_grad():
        for s in range(16):  
            for i in range(len(Is_test)):
                if mixed_precision:
                    with autocast():
                        predictions = scaler*model.predict((X_test[0][nb*i:nb*(i+1)], X_test[1][nb*i:nb*(i+1)], X_test[2]))
                else:
                    predictions = scaler*model.predict((X_test[0][nb*i:nb*(i+1)], X_test[1][nb*i:nb*(i+1)], X_test[2]))
                img1 = torch.from_numpy(scaler*Is_test[nb*i:nb*(i+1), s*nx:(s + 1)*nx].reshape((1,1, 24,24,40)))
                img2 = torch.from_numpy(predictions[:, s*nx:(s + 1)*nx].reshape((1,1,24,24,40)))
                ssims.append(ssim3D(img1,img2).numpy())
    print("Mean 3D SSIM of testing dataset", np.mean(ssims))

    model.net.float().eval()

    from mpl_toolkits.axes_grid1 import make_axes_locatable
    ind_ns = [1, 6, 11, 15]  # 时间步索引
    puff = [0, 11, 14, 19]   # 样本索引
    ind_nk = 1               # 选择的样本索引
    num_plots = len(ind_ns)
    fig, axes = plt.subplots(3, num_plots, figsize=(15, 10), constrained_layout=False)

    # 打印调试信息
    print(f"Xs_test[-1] shape: {Xs_test[-1].shape}")
    print(f"Unique y values: {np.unique(Xs_test[-1][:, 1])}")
    
    # 选择一个 y 值进行切片可视化
    y_value = np.unique(Xs_test[-1][:, 1])[12]  # 选择 y=0.025 附近的值
    y_slice = np.isclose(Xs_test[-1][:nx, 1].flatten(), y_value)
    print(f"y_slice sum: {np.sum(y_slice)}")
    
    # 获取预测结果
    pred1 = model.predict((X_test[0][puff], X_test[1][puff], X_test[2])) * scaler
    print(dde.metrics.l2_relative_error(Is_test[puff] * scaler, pred1))

    # 设置行标签
    row_labels = ["Reference", "Prediction", "Absolute error"]
    for row, label in enumerate(row_labels):
        axes[row, 0].set_ylabel(label, fontsize=15, labelpad=10)

    for i, ns in enumerate(ind_ns):
        # 确保 ns 在有效范围内
        if ns >= 16:
            ns = 15
            
        # 处理 y_slice 没有匹配数据点的情况
        if np.sum(y_slice) == 0:
            print(f"Warning: y_slice has no matching data points for ns={ns}")
            slice_data = scaler * Is_test[puff][ind_nk, ns * nx:(ns + 1) * nx].reshape((24, 24, 40))
            ref_data = slice_data[:, 12, :]  # 取 y 方向的中间切片
        else:
            ref_data = scaler * Is_test[puff][ind_nk, ns * nx:(ns + 1) * nx][y_slice].reshape((24, 40))
            
        # Reference subplot - 使用 level3 的输入 I
        ax = axes[0, i]
        im = ax.imshow(np.rot90(ref_data, k=1),
                    origin="upper", extent=[-0.5, 0.5, 0, 2], vmin=0, vmax=3 * scaler, cmap="inferno")
        ax.set_xticks([-0.5, -0.25, 0, 0.25, 0.5])
        ax.set_title(f"Time step {ns}", fontsize=12)
        if i == num_plots - 1:
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            fig.colorbar(im, cax=cax)

        # Prediction subplot
        ax = axes[1, i]
        if np.sum(y_slice) == 0:
            slice_data = pred1[ind_nk, ns * nx:(ns + 1) * nx].reshape((24, 24, 40))
            pred_data = slice_data[:, 12, :]
        else:
            pred_data = pred1[ind_nk, ns * nx:(ns + 1) * nx][y_slice].reshape((24, 40))
            
        im = ax.imshow(np.rot90(pred_data, k=1),
                    origin="upper", extent=[-0.5, 0.5, 0, 2], vmin=0, vmax=3 * scaler, cmap="inferno")
        ax.set_xticks([-0.5, -0.25, 0, 0.25, 0.5])
        if i == num_plots - 1:
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            fig.colorbar(im, cax=cax)

        # Absolute difference subplot
        ax = axes[2, i]
        if np.sum(y_slice) == 0:
            ref_slice = scaler * Is_test[puff][ind_nk, ns * nx:(ns + 1) * nx].reshape((24, 24, 40))
            pred_slice = pred1[ind_nk, ns * nx:(ns + 1) * nx].reshape((24, 24, 40))
            diff_data = abs(pred_slice[:, 12, :] - ref_slice[:, 12, :])
        else:
            diff_data = abs(pred1[ind_nk, ns * nx:(ns + 1) * nx][y_slice] - Is_test[puff][ind_nk, ns * nx:(ns + 1) * nx][y_slice] * scaler).reshape((24, 40))
            
        im = ax.imshow(np.rot90(diff_data, k=1),  # 使用 k=1 保持与上面相同的方向
                    origin="upper", extent=[-0.5, 0.5, 0, 2], vmin=0, vmax=0.1 * scaler, cmap="inferno")
        ax.set_xticks([-0.5, -0.25, 0, 0.25, 0.5])
        if i == num_plots - 1:
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            fig.colorbar(im, cax=cax)

    plt.subplots_adjust(left=0.12, right=0.9, top=0.9, bottom=0.1, wspace=0.05, hspace=0.2)
    plt.savefig(f"{LOG_PATH}/figs/pred_lthree_wfour_{width}_{modes1}_{modes2}_{modes3}_{nblocks}_{iterations}.png")

    logger.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--width",      type=int, default=64)
    parser.add_argument("--level",      type=str, default="three")
    parser.add_argument("--iterations", type=int, default=150000)
    parser.add_argument("--batch_b",    type=int, default=16)
    parser.add_argument("--batch_t",    type=int, default=16)
    parser.add_argument("--modes1",     type=int, default=15)
    parser.add_argument("--modes2",     type=int, default=15)
    parser.add_argument("--modes3",     type=int, default=10)
    parser.add_argument("--nblocks",    type=int, default=4)

    args = parser.parse_args()
    main(args.level, args.width, args.iterations, args.batch_b, args.batch_t,
         args.modes1, args.modes2, args.modes3, args.nblocks)=n
