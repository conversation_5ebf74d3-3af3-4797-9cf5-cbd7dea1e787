import time
import os
import scipy
import torch
from torch.cuda.amp import autocast
from torch.autograd import profiler
import argparse
import deepxde as dde
import matplotlib.pyplot as plt
import numpy as np
from model_55_60 import *
dde.backend.set_default_backend('pytorch')
dde.config.disable_xla_jit()
import sys
import logging
from typing import *
from datetime import datetime
from sklearn.model_selection import train_test_split
sys.path.append(os.path.abspath(os.path.join(__file__, "../../../src")))
from RTEDeepONet import McCaffrey_fire

PATH = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/data_lfour/"
MODEL_PATH = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/model"
LOG_PATH = "/gpfs/radev/project/lu_lu/wj259/RTEDeepONet/logs"
IS_WITHX = True

class Logger(object):
    def __init__(self, filename="Default.log"):
        self.terminal = sys.stdout
        self.log = open(filename, "a")

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)

    def flush(self):
        self.terminal.flush()
        self.log.flush()

    def isatty(self):
        # pretend we’re never a TTY; or you could return self.terminal.isatty()
        return False

    def close(self):
        # Restore stdout and close the log file properly
        sys.stdout = self.terminal
        self.log.close()

def load_data(level, test_ratio = 100/3999):
    nPhi_ = 2
    nTheta_ = 2
    ns = nTheta_*nPhi_*4
    nx = 30**3

    # Data files in data_lfour directory
    data_files = [
        "lfour_data_54.7507.npz",
        "lfour_data_58.6201.npz",
        "lfour_data_62.5324.npz",
        "lfour_data_66.34.npz",
        "lfour_data_70.1529.npz",
        "lfour_data_73.9086.npz"
    ]

    # Load data from the first file to get X_trunk
    first_data = np.load(f"{PATH}{data_files[0]}")
    X_trunk = first_data["X_trunk"]

    # test all files
    # train_files = [data_files[0], data_files[2]] 
    train_files = data_files
    test_file = data_files[1] 

    # Load training data
    X_branch1_list = []
    X_branch2_list = []
    Is_list = []

    for i, file in enumerate(train_files):
        data = np.load(f"{PATH}{file}")
        X_branch1_list.append(data["X_branch1"])
        X_branch2_list.append(data["X_branch2"])
        Is_list.append(data["I"])
        print(f"Loaded training file {i}: {file}, shapes:", data["X_branch1"].shape, data["X_branch2"].shape, data["I"].shape)

    # Combine training data
    X_branch1 = np.vstack(X_branch1_list)
    X_branch2 = np.vstack(X_branch2_list)
    Is = np.vstack(Is_list)
    print("Combined training data shapes:", X_branch1.shape, X_branch2.shape, Is.shape)

    # Load test data
    test_data = np.load(f"{PATH}{test_file}")
    X1_test = test_data["X_branch1"]
    X2_test = test_data["X_branch2"]
    Is_test = test_data["I"]
    print(f"Loaded test file: {test_file}, shapes:", X1_test.shape, X2_test.shape, Is_test.shape)

    # Split training data for validation
    X1_train, X1_val, X2_train, X2_val, Is_train, Is_val = train_test_split(
        X_branch1, X_branch2, Is, test_size=test_ratio, random_state=42
    )

    # Prepare data for model
    Xs_train = (X1_train.astype(np.float32, copy=False), X2_train.astype(np.float32, copy=False), X_trunk.astype(np.float32, copy=False))
    Is_train = Is_train.astype(np.float32, copy=False)

    Xs_test0 = (X1_test.astype(np.float32, copy=False), X2_test.astype(np.float32, copy=False), X_trunk.astype(np.float32, copy=False))
    Is_test0 = Is_test.astype(np.float32, copy=False)

    # Preprocess data
    Xs_train, Is_train, Xs_test0, Is_test0, T_scaler, k_scaler, scaler = McCaffrey_fire.preprocess_data(
        len(X1_train), 100, Xs_train, Is_train, Xs_test0, Is_test0, isPCA=False, n_pca=0
    )

    # Use a subset for quick testing
    Xs_test = (Xs_test0[0][:20], Xs_test0[1][:20], Xs_test0[-1])
    Is_test = Is_test0[:20]

    print(Xs_train[1].shape, Xs_train[-1].shape, Xs_test[1].shape, Is_train.shape, Is_test.shape)
    return Xs_train, Is_train, Xs_test, Is_test, Xs_test0, Is_test0, scaler


def deeponet_3d_fourier(X_train, I_train, X_test, I_test,
                     isRestore=False, isKAN=False,
                     model_restore_path = f"{MODEL_PATH}/modeltest.pt",
                     model_save_path = f"{MODEL_PATH}/modeltest",
                     output_dir = f"{LOG_PATH}/history",
                     activation = ["relu", "relu", "gelu"],
                     batch_b = 2**3,
                     batch_t = 151**2,
                     n = 128,
                     m = 16,
                     modes1=15,modes2=15,modes3=10,nblocks=4,
                     iterations = 1000,
                     optimizer ="adam",
                     withbc = False):

    nsk = np.prod(X_train[0].shape[1:])
    nst = np.prod(X_train[1].shape[1:])
    dim_x = X_train[-1].shape[-1]
    print("dim_x:", nsk, nst, dim_x)
    print("Activation functions: ", activation)

    for i in range(len(activation)):
        if activation[i] == "gelu":
            activation[i] = torch.nn.GELU()
        elif activation[i] == "swish":
            activation[i] = torch.nn.SiLU()

    data = QuadrupleCartesianProd(X_train, I_train, X_test, I_test)
    print(data.train_x[0].shape, data.train_x[1].shape, data.train_x[-1].shape, data.train_y.shape, data.test_y.shape)

    X_trunk_layers = [dim_x,n,n,n]
    if IS_WITHX:
        branch = branch_withx
    X_branch1_layers = [nsk,branch(n)]
    X_branch2_layers = [nst,branch(n)]

    output_merger_layers = [n, decoder(modes1,modes2,modes3,n, nblocks)]

    net = MIONetCartesianProd(
            X_branch1_layers,
            X_branch2_layers,
            X_trunk_layers,
            {"branch1": activation[0], "branch2": activation[1], "trunk": activation[2], "output merger": activation[2]},
            "Glorot normal",
            regularization=["l2", 1e-6],
            merge_operation="sum",      # use add instead of mul
            trunk_last_activation=True,
            layer_sizes_merger=None,
            output_merge_operation="mul",
            layer_sizes_output_merger=output_merger_layers,
        )


    def output_transform(inputs, outputs):
         return outputs**2

    net.apply_output_transform(output_transform)

    learning_rate = 1e-3
    if optimizer=="adam":
        optimizer = torch.optim.Adam(net.parameters())#, amsgrad=True)
    model = dde.Model(data, net)
    # optimizer ="rmsprop" #"rmsprop" #adamw
    if isRestore:
        model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), loss="mse", metrics=["l2 relative error"]) #loss=loss_func,
        model.restore(model_restore_path)
        print("Model restored from: ", model_restore_path)
    else:
        # model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), loss="mse", metrics=["l2 relative error"]) #loss=loss_func,
        # model.restore(model_restore_path)
        # print("Model restored from: ", model_restore_path)
        print("Batch size of branch and trunk:", (batch_b, batch_t), ", Optimizer:", optimizer, ", #Iterations:", iterations)
        model.compile(optimizer, lr=learning_rate,  decay=("inverse time", iterations // 100, 1e-3),  metrics=["l2 relative error"])
        checkpointer = dde.callbacks.ModelCheckpoint(model_save_path, save_better_only=True, period=2000)
        # print("# Parameters--", net.num_trainable_parameters())
        # xyz
        losshistory, train_state = model.train(iterations=iterations, batch_size= batch_b , display_every=500, callbacks=[checkpointer], model_save_path=model_save_path)
        dde.saveplot(losshistory, train_state, issave=True, isplot=False, output_dir=output_dir)
    print("# Parameters:", net.num_trainable_parameters())

    return model

def main(level, width, iterations, batch_b, batch_t, modes1, modes2, modes3, nblocks):
    Xs_train, Is_train, Xs_test, Is_test, Xs_test0, Is_test0, scaler = load_data(level, test_ratio = 100/3999)

    # current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    #--width 64 --level four --iterations 150000 --batch_b 16 --batch_t 16 --modes1 15 --modes2 15 --modes3 10 --nblocks 4
    filename = f"{LOG_PATH}/log_lfour_{width}_{modes1}_{modes2}_{modes3}_{nblocks}_{iterations}.log"
    logger = Logger(filename)
    sys.stdout = logger

    train_shape, test_shape = (len(Is_train), 30, 30, 30, 1), (len(Is_test), 30, 30, 30, 1)
    test_shape0 = (len(Is_test0), 30, 30, 30, 1)
    unique_ps, unique_ts = np.meshgrid(np.unique(Xs_train[-1][:, 3]), np.unique(Xs_train[-1][:, 4]))

    if IS_WITHX:
        grid_x, grid_y, grid_z = np.mgrid[-1.45:1.45:30j, -1.45:1.45:30j, 0.05:2.95:30j]
        new_points = np.vstack((grid_x.flatten(), grid_y.flatten(), grid_z.flatten())).T.reshape(30, 30, 30, 3)
        new_points_train = np.repeat(new_points[None, ...], len(Is_train), axis=0).astype(np.float32)
        new_points_test = np.repeat(new_points[None, ...], len(Is_test), axis=0).astype(np.float32)
        new_points_test0 = np.repeat(new_points[None, ...], len(Is_test0), axis=0).astype(np.float32)

        X_train, X_test = (np.concatenate((new_points_train, Xs_train[0].reshape(train_shape)), axis=-1),
                        np.concatenate((new_points_train, Xs_train[1].reshape(train_shape)), axis=-1),
                        np.column_stack((unique_ps.flatten(), unique_ts.flatten()))),\
                        (np.concatenate((new_points_test, Xs_test[0].reshape(test_shape)), axis=-1),
                        np.concatenate((new_points_test, Xs_test[1].reshape(test_shape)), axis=-1),
                        np.column_stack((unique_ps.flatten(), unique_ts.flatten())))
        X_test0 = (np.concatenate((new_points_test0, Xs_test0[0].reshape(test_shape0)), axis=-1),
                   np.concatenate((new_points_test0, Xs_test0[1].reshape(test_shape0)), axis=-1),
                    np.column_stack((unique_ps.flatten(), unique_ts.flatten())))
        print(X_train[0].shape, X_train[-1].shape, X_test[1].shape, Is_train.shape, Is_test.shape)

    else:
        X_train, X_test = (Xs_train[0].reshape(train_shape), Xs_train[1].reshape(train_shape),
                        np.column_stack((unique_ps.flatten(), unique_ts.flatten()))),\
                        (Xs_test[0].reshape(test_shape), Xs_test[1].reshape(test_shape),
                            np.column_stack((unique_ps.flatten(), unique_ts.flatten())))
        X_test0 = (Xs_test0[0].reshape(test_shape0), Xs_test0[1].reshape(test_shape0),
                    np.column_stack((unique_ps.flatten(), unique_ts.flatten())))
        print("No x:", X_train[0].shape, X_train[-1].shape, X_test[1].shape, Is_train.shape, Is_test.shape)

    model = deeponet_3d_fourier(X_train, Is_train, X_test, Is_test,
                            isRestore=False, isKAN=False,
                            model_restore_path = f"{MODEL_PATH}/modeltest.pt",
                            model_save_path = f"{MODEL_PATH}/modeltest",
                            output_dir = f"{LOG_PATH}/history",
                            activation = ["relu", "relu", "swish"],
                            batch_b = batch_b,
                            batch_t = batch_t,
                            n = width,
                            modes1=modes1,modes2=modes2,modes3=modes3,nblocks=nblocks,
                            iterations = iterations,
                            optimizer = "adam")

    # device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")
    # model.net.to(device)
    # model.net.eval()

    # nb, i0 = 1, 0
    # for i0 in range(5):
    #     _ = model.predict((X_test[0][i0:i0+nb], X_test[1][i0:i0+nb], X_test[2]))

    # if device.type == "cuda":
    #     torch.cuda.synchronize()
    # t0 = time.time()
    # for i0 in range(10):
    #     _ = model.predict((X_test[0][i0:i0+nb], X_test[1][i0:i0+nb], X_test[2]))
    # if device.type == "cuda":
    #     torch.cuda.synchronize()
    # print(f"Inference time (batch={nb}): {(time.time()-t0)/10:.4f}s")

    # with torch.no_grad(), profiler.profile(record_shapes=True, use_cuda=True) as prof:
    #     with profiler.record_function("model_inference"), autocast():
    #         out = model.predict((X_test[0][i0:i0+nb], X_test[1][i0:i0+nb], X_test[2]))
    # print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=15))
    # xyz

    mixed_precision = False
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    net = model.net
    net.to(device).eval()
    if mixed_precision:
        net.half()

    dtype = torch.float16 if mixed_precision else torch.float32
    X0_all = torch.from_numpy(X_test[0]).to(device, dtype=dtype, non_blocking=True)
    X1_all = torch.from_numpy(X_test[1]).to(device, dtype=dtype, non_blocking=True)
    X2_all  = torch.from_numpy(X_test[2]).to(device, dtype=dtype, non_blocking=True)

    # warm-up
    with torch.no_grad():
        for _ in range(5):
            if mixed_precision:
                with autocast():
                    _ = net((X0_all[:1], X1_all[:1], X2_all))
            else:
                _ = net((X0_all[:1], X1_all[:1], X2_all))

    # Inference timing https://medium.com/@MarkAiCode/mastering-pytorch-inference-time-measurement-22da0eaebab7
    nb = 1
    torch.cuda.synchronize()
    t0 = time.time()
    with torch.no_grad():
        for i in range(10):
            if mixed_precision:
                with autocast():
                    _ = net((X0_all[i:i+nb], X1_all[i:i+nb], X2_all))
            else:
                _ = net((X0_all[i:i+nb], X1_all[i:i+nb], X2_all))
    torch.cuda.synchronize()
    print(f"Inference time (batch={nb}): {(time.time() - t0)/10:.4f}s")

    pred_test = []
    test_errors = []
    nb = 1
    nx = 30**3
    with torch.no_grad():
        for i in range(len(Is_test0)):
            if mixed_precision:
                with autocast():
                    predictions = model.predict((X_test0[0][nb*i:nb*(i+1)], X_test0[1][nb*i:nb*(i+1)], X_test0[-1]))
            else:
                predictions = model.predict((X_test0[0][nb*i:nb*(i+1)], X_test0[1][nb*i:nb*(i+1)], X_test0[-1]))
            err = dde.metrics.mean_l2_relative_error(scaler*Is_test0[nb*i:nb*(i+1)], scaler*predictions)
            test_errors.append(err)
            pred_test.append(predictions)
            # print(err)
        print("Mean l2 relative error of testing dataset", np.mean(test_errors))

    i0 = 0

    if mixed_precision:
        with torch.no_grad(), profiler.profile(record_shapes=True, use_cuda=True) as prof:
            with profiler.record_function("model_inference"), autocast():
                out = net((X0_all[i0:i0+nb], X1_all[i0:i0+nb], X2_all))
                # predictions = model.predict((X_test0[0][nb*i:nb*(i+1)], X_test0[1][nb*i:nb*(i+1)], X_test0[-1]))
        print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=15))
    else:
        with torch.no_grad(), profiler.profile(record_shapes=True, use_cuda=True) as prof:
            with profiler.record_function("model_inference"):
                out = net((X0_all[i0:i0+nb], X1_all[i0:i0+nb], X2_all))
                # predictions = model.predict((X_test0[0][nb*i:nb*(i+1)], X_test0[1][nb*i:nb*(i+1)], X_test0[-1]))
        print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=15))

    from RTEDeepONet.ssim3d import ssim3D
    ssims = []
    nb = 1
    nx = 30**3
    with torch.no_grad():
        for s in range(16):
            for i in range(len(Is_test0)):
                if mixed_precision:
                    with autocast():
                        predictions = scaler*model.predict((X_test0[0][nb*i:nb*(i+1)], X_test0[1][nb*i:nb*(i+1)], X_test0[-1]))
                else:
                    predictions = scaler*model.predict((X_test0[0][nb*i:nb*(i+1)], X_test0[1][nb*i:nb*(i+1)], X_test0[-1]))
                img1 = torch.from_numpy(scaler*Is_test0[nb*i:nb*(i+1), s*nx:(s + 1)*nx].reshape((1,1, 30,30,30)))
                img2 = torch.from_numpy(predictions[:, s*nx:(s + 1)*nx].reshape((1,1,30,30,30)))
                ssims.append(ssim3D(img1,img2).numpy())
    print("Mean 3D SSIM of testing dataset", np.mean(ssims))

    model.net.float().eval()

    from mpl_toolkits.axes_grid1 import make_axes_locatable
    ind_ns = [1, 6, 11, 15]
    puff = [0, 11, 14, 19]
    ind_nk = 1
    num_plots = len(ind_ns)
    fig, axes = plt.subplots(3, num_plots, figsize=(15, 10), constrained_layout=False)

    y_slice = np.isclose(Xs_test[-1][:nx, 1].flatten(), 0.05)
    pred1 = model.predict((X_test[0][puff], X_test[1][puff], X_test[-1])) * scaler
    print(dde.metrics.l2_relative_error(Is_test[puff] * scaler, pred1))

    row_labels = ["Reference", "Prediction", "Absolute error"]
    for row, label in enumerate(row_labels):
        axes[row, 0].set_ylabel(label, fontsize=15, labelpad=10)  # Set y-axis label for the first column

    for i, ns in enumerate(ind_ns):
        # Reference subplot
        ax = axes[0, i]
        im = ax.imshow(np.rot90(scaler * Is_test[puff][ind_nk, ns * nx:(ns + 1) * nx][y_slice].reshape((30, 30)), k=1),
                    origin="upper", extent=[-1.5, 1.5, 0, 3], vmin=0, vmax=3 * scaler, cmap="inferno")
        ax.set_xticks([-1.5, -1.0, -0.5, 0, 0.5, 1.0, 1.5])
        #ax.set_title(f'I (ns={ns})')
        #ax.set_title(f'$I(s=s_{ns})$', fontsize=15)
        if i == num_plots - 1:
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            fig.colorbar(im, cax=cax)

        # Prediction subplot
        ax = axes[1, i]
        im = ax.imshow(np.rot90(pred1[ind_nk, ns * nx:(ns + 1) * nx][y_slice].reshape((30, 30)), k=1),
                    origin="upper", extent=[-1.5, 1.5, 0, 3], vmin=0, vmax=3 * scaler, cmap="inferno")
        ax.set_xticks([-1.5, -1.0, -0.5, 0, 0.5, 1.0, 1.5])
        #ax.set_title('Prediction')
        if i == num_plots - 1:
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            fig.colorbar(im, cax=cax)

        # Absolute difference subplot
        ax = axes[2, i]
        im = ax.imshow(np.rot90(abs(pred1[ind_nk, ns * nx:(ns + 1) * nx][y_slice] - Is_test[puff][ind_nk, ns * nx:(ns + 1) * nx][y_slice] * scaler).reshape((30, 30)), k=3),
                    origin="lower", extent=[-1.5, 1.5, 0, 3], vmin=0, vmax=0.1 * scaler, cmap="inferno")
        ax.set_xticks([-1.5, -1.0, -0.5, 0, 0.5, 1.0, 1.5])
        #ax.set_title('Absolute error')
        if i == num_plots - 1:
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            fig.colorbar(im, cax=cax)

    plt.subplots_adjust(left=0.12, right=0.9, top=0.9, bottom=0.1, wspace=0.05, hspace=0.2)
    plt.savefig(f"{LOG_PATH}/figs/pred_lfour_{width}_{modes1}_{modes2}_{modes3}_{nblocks}_{iterations}.png")

    logger.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--width",      type=int, default=64)
    parser.add_argument("--level",      type=str, default="four")
    parser.add_argument("--iterations", type=int, default=150000)
    parser.add_argument("--batch_b",    type=int, default=16)
    parser.add_argument("--batch_t",    type=int, default=16)
    parser.add_argument("--modes1",    type=int, default=15)
    parser.add_argument("--modes2",    type=int, default=15)
    parser.add_argument("--modes3",    type=int, default=10)
    parser.add_argument("--nblocks",    type=int, default=4)

    args = parser.parse_args()
    main(args.level, args.width, args.iterations, args.batch_b, args.batch_t,
         args.modes1, args.modes2, args.modes3, args.nblocks)