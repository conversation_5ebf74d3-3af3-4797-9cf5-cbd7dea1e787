import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import torch.optim as optim
from typing import *
import deepxde as dde
from deepxde.nn.nn import NN
from deepxde.nn import activations
from deepxde.data.data import Data
from deepxde.data.sampler import BatchSampler
from deepxde.nn.pytorch.fnn import FNN

class QuadrupleCartesianProd(Data):
    """Cartesian Product input data format for MIONet architecture.

    This dataset can be used with the network ``MIONetCartesianProd`` for operator
    learning.

    Args:
        X_train: A tuple of three NumPy arrays. The first element has the shape (`N1`,
            `dim1`), the second element has the shape (`N1`, `dim2`), and the third
            element has the shape (`N2`, `dim3`).
        y_train: A NumPy array of shape (`N1`, `N2`).
    """

    def __init__(self, X_train, y_train, X_test, y_test):
        self.indices_branch, self.indices_trunk = None, None
        self.train_x, self.train_y = X_train, y_train
        self.test_x, self.test_y = X_test, y_test

        self.branch_sampler = BatchSampler(len(X_train[0]), shuffle=True)
        self.trunk_sampler = BatchSampler(len(X_train[2]), shuffle=True)

    def losses(self, targets, outputs, loss_fn, inputs, model, aux=None):
        return loss_fn(targets, outputs)

    def train_next_batch(self, batch_size=None):
        if batch_size is None:
            return self.train_x, self.train_y
        if not isinstance(batch_size, (tuple, list)):
            self.indices_branch = self.branch_sampler.get_next(batch_size)
            return (
                self.train_x[0][self.indices_branch],
                self.train_x[1][self.indices_branch],
                self.train_x[2],
            ), self.train_y[self.indices_branch]
        self.indices_branch = self.branch_sampler.get_next(batch_size[0])
        self.indices_trunk = self.trunk_sampler.get_next(batch_size[1])
        return (
            self.train_x[0][self.indices_branch],
            self.train_x[1][self.indices_branch],
            self.train_x[2][self.indices_trunk],
        ), self.train_y[self.indices_branch,:][:, self.indices_trunk]

    def test(self):
        return self.test_x, self.test_y

class MIONetCartesianProd(NN):
    """MIONet with two input functions for Cartesian product format."""

    def __init__(
        self,
        layer_sizes_branch1,
        layer_sizes_branch2,
        layer_sizes_trunk,
        activation,
        kernel_initializer,
        regularization=None,
        trunk_last_activation=False,
        merge_operation="mul",
        layer_sizes_merger=None,
        output_merge_operation="mul",
        layer_sizes_output_merger=None
    ):
        super().__init__()

        if isinstance(activation, dict):
            self.activation_branch1 = activations.get(activation["branch1"])
            self.activation_branch2 = activations.get(activation["branch2"])
            self.activation_trunk = activations.get(activation["trunk"])
        else:
            self.activation_branch1 = (
                self.activation_branch2
            ) = self.activation_trunk = activations.get(activation)
        if callable(layer_sizes_branch1[1]):
            # User-defined network
            self.branch1 = layer_sizes_branch1[1]
        else:
            # Fully connected network
            self.branch1 = FNN(
                layer_sizes_branch1, self.activation_branch1, kernel_initializer
            )
        if callable(layer_sizes_branch2[1]):
            # User-defined network
            self.branch2 = layer_sizes_branch2[1]
        else:
            # Fully connected network
            self.branch2 = FNN(
                layer_sizes_branch2, self.activation_branch2, kernel_initializer
            )
        if layer_sizes_merger is not None:
            self.activation_merger = activations.get(activation["merger"])
            if callable(layer_sizes_merger[1]):
                # User-defined network
                self.merger = layer_sizes_merger[1]
            else:
                # Fully connected network
                self.merger = FNN(
                    layer_sizes_merger, self.activation_merger, kernel_initializer
                )
        else:
            self.merger = None
        if layer_sizes_output_merger is not None:
            self.activation_output_merger = activations.get(activation["output merger"])
            if callable(layer_sizes_output_merger[1]):
                # User-defined network
                self.output_merger = layer_sizes_output_merger[1]
            else:
                # Fully connected network
                self.output_merger = FNN(
                    layer_sizes_output_merger, self.activation_output_merger, kernel_initializer
                )
        else:
            self.output_merger = None
        if callable(layer_sizes_trunk[1]):
            # User-defined network
            self.trunk = layer_sizes_trunk[1]
        else:
            # Fully connected network
            self.trunk = FNN(layer_sizes_trunk, self.activation_trunk, kernel_initializer)
        self.b = torch.tensor(0.0, requires_grad=True)
        self.regularizer = regularization
        self.trunk_last_activation = trunk_last_activation
        self.merge_operation = merge_operation
        self.output_merge_operation = output_merge_operation

    def forward(self, inputs):
        x_func1 = inputs[0]
        x_func2 = inputs[1]
        x_loc = inputs[2]
        # print(x_func1.shape, x_func2.shape, x_loc.shape)
        # torch.Size([2, 30, 30, 30]) torch.Size([2, 30, 30, 30]) torch.Size([16, 2])
        
        # Branch net to encode the input function
        y_func1 = self.branch1(x_func1)
        y_func2 = self.branch2(x_func2)
        # print(y_func1.shape, y_func2.shape)
        #torch.Size([2, 30, 30, 30, 64]) torch.Size([2, 30, 30, 30, 64])
        if self.merge_operation == "sum":
            x_merger = y_func1 + y_func2
        elif self.merge_operation == "mul":
            x_merger = torch.mul(y_func1, y_func2)
        else:
            raise NotImplementedError(
                f"{self.merge_operation} operation to be implimented"
            )
        # Optional merger net
        if self.merger is not None:
            y_func = self.merger(x_merger)
        else:
            y_func = x_merger
        # Trunk net to encode the domain of the output function
        if self._input_transform is not None:
            y_loc = self._input_transform(x_loc)
        # trunk net
        y_loc = self.trunk(x_loc)
        #print(x_loc.shape, y_loc.shape, y_func.shape) 
        # torch.Size([16, 2]) torch.Size([16, 64]) torch.Size([2, 64, 30, 30, 30])

        if self.trunk_last_activation:
            y_loc = self.activation_trunk(y_loc)

        # output merger net
        if self.output_merger is None:
            y = torch.einsum("ip,jp->ij", y_func, y_loc)
        else:
            y_func = y_func[:, None, :, :, :, :]
            y_loc = y_loc[None, :, :, None, None, None]
            if self.output_merge_operation == "mul":
                y = torch.mul(y_func,y_loc)
            elif self.output_merge_operation == "sum":
                y = y_func+y_loc
            # torch.Size([2, 1, 64, 30, 30, 30]) torch.Size([1, 16, 64, 1, 1, 1]) torch.Size([2, 16, 64, 30, 30, 30])
            #print(y_func.shape, y_loc.shape, y.shape)
            batch_size = y.shape[0]
            s_size = y.shape[1]
            channal_num = y.shape[2]
            x_size = y.shape[3]
            y_size = y.shape[4]
            z_size = y.shape[5]
            y = y.reshape(batch_size * s_size, channal_num, x_size, y_size, z_size)
            y = self.output_merger(y)
            y = y.reshape(batch_size, s_size*x_size*y_size*z_size)
        # Add bias
        # y = y + self.b
        if self._output_transform is not None:
            y = self._output_transform(inputs, y)
        return y


class decoder(nn.Module):
    def __init__(self, modes1, modes2, modes3, width, num_blocks=4):
        super(decoder, self).__init__()

        self.modes1 = modes1
        self.modes2 = modes2
        self.modes3 = modes3
        self.width = width
        self.width2 = width * 4

        self.convs = nn.ModuleList([
             SpectralConv3d(self.width, self.width, modes1, modes2, modes3)
             for _ in range(num_blocks)
         ])
        self.ws    = nn.ModuleList([
             nn.Conv1d(self.width, self.width, 1)
             for _ in range(num_blocks)
         ])
        self.fc1 = nn.Linear(self.width, self.width2)
        self.fc2 = nn.Linear(self.width2, 1)

    def forward(self, x):
        batchsize = x.shape[0]
        size_x, size_y, size_z = x.shape[2], x.shape[3], x.shape[4]
        # print("size_x, size_y, size_z:", batchsize, size_x, size_y, size_z)
        
        for conv, w in zip(self.convs, self.ws):
            x1 = conv(x)
            x2 = w(x.view(batchsize, self.width, -1)).view(batchsize, self.width, size_x, size_y, size_z)
            x  = F.relu(x1 + x2)
        
        x = x.permute(0, 2, 3, 4, 1)
        x = self.fc1(x)
        x = F.relu(x)
        x = self.fc2(x)
        
        return x.squeeze()

class SpectralConv3d(nn.Module):
    def __init__(self, in_channels, out_channels, modes1, modes2, modes3):
        super(SpectralConv3d, self).__init__()
        """
        3D Fourier layer. It does FFT, linear transform, and Inverse FFT.    
        """
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.modes1 = modes1  # Number of Fourier modes to multiply, at most floor(N/2) + 1
        self.modes2 = modes2
        self.modes3 = modes3

        self.scale = (1 / (in_channels * out_channels))
        self.weights1 = nn.Parameter(self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, self.modes3, dtype=torch.cfloat))
        self.weights2 = nn.Parameter(self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, self.modes3, dtype=torch.cfloat))
        self.weights3 = nn.Parameter(self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, self.modes3, dtype=torch.cfloat))
        self.weights4 = nn.Parameter(self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, self.modes3, dtype=torch.cfloat))

    def compl_mul3d(self, input, weights):
        # (batch, in_channel, x,y), (in_channel, out_channel, x,y) -> (batch, out_channel, x,y)
        return torch.einsum("bixyz,ioxyz->boxyz", input, weights)

    def forward(self, x):
        fp16_input = x.dtype == torch.float16
        if fp16_input:
            x = x.float() 
        batchsize = x.shape[0]
        # Compute Fourier coeffcients up to factor of e^(- something constant)
        x_ft = torch.fft.rfftn(x, dim=[-3, -2, -1])

        # Multiply relevant Fourier modes
        out_ft = torch.zeros(batchsize, self.out_channels, x.size(-3), x.size(-2), x.size(-1)//2 + 1, dtype=torch.cfloat, device=x.device)
        out_ft[:, :, :self.modes1, :self.modes2, :self.modes3] = \
            self.compl_mul3d(x_ft[:, :, :self.modes1, :self.modes2, :self.modes3], self.weights1)
        out_ft[:, :, -self.modes1:, :self.modes2, :self.modes3] = \
            self.compl_mul3d(x_ft[:, :, -self.modes1:, :self.modes2, :self.modes3], self.weights2)
        out_ft[:, :, :self.modes1, -self.modes2:, :self.modes3] = \
            self.compl_mul3d(x_ft[:, :, :self.modes1, -self.modes2:, :self.modes3], self.weights3)
        out_ft[:, :, -self.modes1:, -self.modes2:, :self.modes3] = \
            self.compl_mul3d(x_ft[:, :, -self.modes1:, -self.modes2:, :self.modes3], self.weights4)

        # Return to physical space
        x = torch.fft.irfftn(out_ft, s=(x.size(-3), x.size(-2), x.size(-1)))
        if fp16_input:
            x = x.half()
        return x
    
    # def forward(self, x):
    #     B, C, nx, ny, nz = x.shape

    #     # 1) Next power-of-two (32 for a 30³ grid)
    #     def next_pow2(n):
    #         return 1 << ((n - 1).bit_length())
    #     nx2, ny2, nz2 = next_pow2(nx), next_pow2(ny), next_pow2(nz)

    #     # 2) Symmetric padding amounts
    #     px, py, pz = nx2 - nx, ny2 - ny, nz2 - nz
    #     px0, px1 = px // 2, px - px // 2
    #     py0, py1 = py // 2, py - py // 2
    #     pz0, pz1 = pz // 2, pz - pz // 2

    #     # 3) Pad real‐valued x (still half or float32)
    #     #    F.pad’s pad tuple is (z0,z1, y0,y1, x0,x1)
    #     x_p = F.pad(x, (pz0, pz1, py0, py1, px0, px1))

    #     # 4) FFT on the padded grid: 
    #     #    - If x_p is float16 → this gives ComplexHalf on your 32³
    #     #    - If x_p is float32 → this gives ComplexFloat
    #     x_ft = torch.fft.rfftn(x_p, dim=[-3, -2, -1])

    #     # 5) If we got ComplexHalf, up-cast to ComplexFloat for the multiply
    #     wanted_cplx = torch.complex64
    #     if x_ft.dtype == torch.complex32:   # ComplexHalf
    #         x_ft = x_ft.to(wanted_cplx)

    #     # 6) Prepare an output buffer in ComplexFloat
    #     out_ft = torch.zeros(
    #         B, self.out_channels, nx2, ny2, nz2 // 2 + 1,
    #         dtype=wanted_cplx, device=x.device
    #     )

    #     # 7) Pull your stored weights (originally ComplexFloat) into ComplexFloat
    #     w1 = self.weights1
    #     w2 = self.weights2
    #     w3 = self.weights3
    #     w4 = self.weights4

    #     # 8) Do your spectral multiplies in ComplexFloat
    #     out_ft[:, :, :self.modes1, :self.modes2, :self.modes3] = \
    #         self.compl_mul3d(
    #             x_ft[:, :, :self.modes1, :self.modes2, :self.modes3],
    #             w1
    #         )
    #     out_ft[:, :, -self.modes1:, :self.modes2, :self.modes3] = \
    #         self.compl_mul3d(
    #             x_ft[:, :, -self.modes1:, :self.modes2, :self.modes3],
    #             w2
    #         )
    #     out_ft[:, :, :self.modes1, -self.modes2:, :self.modes3] = \
    #         self.compl_mul3d(
    #             x_ft[:, :, :self.modes1, -self.modes2:, :self.modes3],
    #             w3
    #         )
    #     out_ft[:, :, -self.modes1:, -self.modes2:, :self.modes3] = \
    #         self.compl_mul3d(
    #             x_ft[:, :, -self.modes1:, -self.modes2:, :self.modes3],
    #             w4
    #         )

    #     # 9) If the original FFT was ComplexHalf, down-cast back now
    #     if x.dtype == torch.float16:
    #         out_ft = out_ft.to(torch.complex32)

    #     # 10) Inverse FFT back to real (Half or Float as appropriate)
    #     x_ifft = torch.fft.irfftn(
    #         out_ft, s=(nx2, ny2, nz2), dim=[-3, -2, -1]
    #     )

    #     # 11) Crop back to your original 30³
    #     x_crop = x_ifft[
    #         :,
    #         :,
    #         px0:px0+nx,
    #         py0:py0+ny,
    #         pz0:pz0+nz
    #     ]

    #     return x_crop

class branch(nn.Module):
    def __init__(self,width):
        super(branch, self).__init__()
        self.width = width
        self.fc0 = nn.Linear(1, self.width)


    def forward(self, x):
        x = self.fc0(x)
        x = x.permute(0, 4, 1, 2, 3)
        return x

class branch_withx(nn.Module):
    def __init__(self,width):
        super(branch_withx, self).__init__()
        self.width = width
        self.fc0 = nn.Linear(4, self.width)


    def forward(self, x):
        x = self.fc0(x)
        x = x.permute(0, 4, 1, 2, 3)
        return x

class branch1(nn.Module):
    def __init__(self,width):
        super(branch1, self).__init__()
        self.width = width
        self.fc0 = nn.Linear(2, self.width)


    def forward(self, x):
        x = self.fc0(x)
        x = x.permute(0, 4, 1, 2, 3)
        return x

class branchDeconv3(nn.Module):
    def __init__(self, width):
        super(branchDeconv3, self).__init__()
        self.width = width
        self.fc0 = nn.Linear(16, self.width)
        self.conv1 = nn.Conv3d(in_channels=16, out_channels=16, kernel_size=5)
        self.conv2 = nn.Conv3d(in_channels=16, out_channels=16, kernel_size=5)
        self.deconv = nn.ConvTranspose3d(in_channels=16, out_channels=16, kernel_size=(5, 5, 2), stride = (1,1,2), padding = (1,1,2))

    def forward(self, x):
        x = x.permute(0, 4, 1, 2, 3)
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = self.deconv(x)
        x = x.permute(0, 2, 3, 4, 1)
        x = self.fc0(x)
        x = x.permute(0, 4, 1, 2, 3)
        return x

class branchDeconv2(nn.Module):
    def __init__(self, width):
        super(branchDeconv2, self).__init__()
        self.width = width
        self.fc0 = nn.Linear(16, self.width)
        self.deconv1 = nn.ConvTranspose3d(in_channels=16, out_channels=16, kernel_size=(10, 10, 1))
        self.deconv2 = nn.ConvTranspose3d(in_channels=16, out_channels=16, kernel_size=(8, 8, 1))

    def forward(self, x):
        x = x.permute(0, 4, 1, 2, 3)
        x = self.deconv1(x)
        x = self.deconv2(x)
        x = x.permute(0, 2, 3, 4, 1)
        x = self.fc0(x)
        x = x.permute(0, 4, 1, 2, 3)
        return x

class branchDeconv1(nn.Module):
    def __init__(self, width):
        super(branchDeconv1, self).__init__()
        self.width = width
        self.fc0 = nn.Linear(16, self.width)
        self.deconv1 = nn.ConvTranspose3d(in_channels=16, out_channels=16, kernel_size=(5, 5, 4), stride=(1, 1, 2), padding=(0, 0, 1))
        self.deconv2 = nn.ConvTranspose3d(in_channels=16, out_channels=16, kernel_size=(5, 5, 3), stride=(1, 1, 1), padding=(0, 0, 1))
        
    def forward(self, x):
        x = x.permute(0, 4, 1, 2, 3)
        x = self.deconv1(x)
        x = self.deconv2(x)
        x = x.permute(0, 2, 3, 4, 1)
        x = self.fc0(x)
        x = x.permute(0, 4, 1, 2, 3)
        return x


def deeponet_3d_fourier_nested3(X_train, I_train, X_test, I_test, 
                     isRestore=False, isKAN=False,
                     model_restore_path = "../../model/model2d_pool_weighted-200000.pt",
                     model_save_path = "../../model/model2d_pool_100",
                     output_dir = "../../logs/history",
                     activation = ["relu", "relu", "gelu"],
                     batch_b = 2**3,
                     batch_t = 151**2,
                     n = 128,
                     m=16,
                     iterations = 300000,
                     optimizer ="adam",
                     withbc = False):  
    
    nsk =  X_train[0].shape[0]*X_train[0].shape[1]*X_train[0].shape[2]*X_train[0].shape[3]
    nst = X_train[1].shape[0]*X_train[1].shape[1]*X_train[1].shape[2]*X_train[1].shape[3]
    dim_x = X_train[-1].shape[-1]
    print("dim_x:", nsk, nst, dim_x)
    print("Activation functions: ", activation)
    for i in range(len(activation)):
        if activation[i] == "gelu":
            activation[i] = torch.nn.GELU()
        elif activation[i] == "swish":
            activation[i] = torch.nn.SiLU()

    data = QuadrupleCartesianProd(X_train, I_train, X_test, I_test)
    # (3979, 30, 30, 30, 16) (3979, 24, 24, 40, 2) (16, 2) (3979, 368640) (20, 368640)
    print(data.train_x[0].shape, data.train_x[1].shape, data.train_x[-1].shape, data.train_y.shape, data.test_y.shape)

    if isKAN:
        trunk_net = KANNet(layers_hidden=[dim_x, m, m, n],
                            grid_min = -1,
                            grid_max = 1,
                            num_grids = 50,
                            use_base_update = True,
                            use_layernorm = False,
                            base_activation = F.silu, #nn.GELU(), 
                            spline_weight_init_scale = 0.01,) #0.1
        X_trunk_layers = [None,trunk_net]
        print("trunk_net: ", trunk_net)
    else:
        X_trunk_layers = [dim_x,n,n,n,n]
    
    X_branch1_layers = [nsk,branchDeconv3(n)]
    X_branch2_layers = [nst,branch1(n)]

    output_merger_layers = [n, decoder(10,10,10,n)]

    net = MIONetCartesianProd(
            X_branch1_layers,
            X_branch2_layers,
            X_trunk_layers,
            {"branch1": activation[0], "branch2": activation[1], "trunk": activation[2], "output merger": activation[2]},
            "Glorot normal",
            regularization=["l2", 1e-6],
            merge_operation="mul",  
            trunk_last_activation=True, # add activation to the last layer of the trunk net
            layer_sizes_merger=None,
            output_merge_operation="mul",
            layer_sizes_output_merger=output_merger_layers,
        )


    def output_transform(inputs, outputs):
         return outputs**2

    net.apply_output_transform(output_transform)

    learning_rate = 1e-3
    if optimizer=="adam":
        optimizer = torch.optim.Adam(net.parameters())#, amsgrad=True)
    model = dde.Model(data, net)
    # optimizer ="rmsprop" #"rmsprop" #adamw
    if isRestore:
        model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), loss="mse", metrics=["l2 relative error"]) #loss=loss_func, 
        model.restore(model_restore_path)
        print("Model restored from: ", model_restore_path)
    else:  
        print("Batch size of branch and trunk:", (batch_b, batch_t), ", Optimizer:", optimizer, ", #Iterations:", iterations)
        model.compile(optimizer, lr=learning_rate,  decay=("inverse time", iterations // 100, 1e-3),  metrics=["l2 relative error"])
        checkpointer = dde.callbacks.ModelCheckpoint(model_save_path, save_better_only=True, period=2000)
        losshistory, train_state = model.train(iterations=iterations, batch_size= batch_b , display_every=500, callbacks=[checkpointer], model_save_path=model_save_path)
        dde.saveplot(losshistory, train_state, issave=True, isplot=False, output_dir=output_dir)
    print("# Parameters:", net.num_trainable_parameters())

    return model


def deeponet_3d_fourier_nested2(X_train, I_train, X_test, I_test, 
                     isRestore=False, isKAN=False,
                     model_restore_path = "../../model/model2d_pool_weighted-200000.pt",
                     model_save_path = "../../model/model2d_pool_100",
                     output_dir = "../../logs/history",
                     activation = ["relu", "relu", "gelu"],
                     batch_b = 2**3,
                     batch_t = 151**2,
                     n = 128,
                     m=16,
                     iterations = 300000,
                     optimizer ="adam",
                     withbc = False):  
    
    nsk =  X_train[0].shape[0]*X_train[0].shape[1]*X_train[0].shape[2]*X_train[0].shape[3]
    nst = X_train[1].shape[0]*X_train[1].shape[1]*X_train[1].shape[2]*X_train[1].shape[3]
    dim_x = X_train[-1].shape[-1]
    print("dim_x:", nsk, nst, dim_x)
    print("Activation functions: ", activation)
    for i in range(len(activation)):
        if activation[i] == "gelu":
            activation[i] = torch.nn.GELU()
        elif activation[i] == "swish":
            activation[i] = torch.nn.SiLU()

    data = QuadrupleCartesianProd(X_train, I_train, X_test, I_test)
    # (3979, 30, 30, 30, 16) (3979, 24, 24, 40, 2) (16, 2) (3979, 368640) (20, 368640)
    print(data.train_x[0].shape, data.train_x[1].shape, data.train_x[-1].shape, data.train_y.shape, data.test_y.shape)

    if isKAN:
        trunk_net = KANNet(layers_hidden=[dim_x, m, m, n],
                            grid_min = -1,
                            grid_max = 1,
                            num_grids = 50,
                            use_base_update = True,
                            use_layernorm = False,
                            base_activation = F.silu, #nn.GELU(), 
                            spline_weight_init_scale = 0.01,) #0.1
        X_trunk_layers = [None,trunk_net]
        print("trunk_net: ", trunk_net)
    else:
        X_trunk_layers = [dim_x,n,n,n,n]
    
    X_branch1_layers = [nsk,branchDeconv2(n)]
    X_branch2_layers = [nst,branch1(n)]

    output_merger_layers = [n, decoder(10,10,10,n)]

    net = MIONetCartesianProd(
            X_branch1_layers,
            X_branch2_layers,
            X_trunk_layers,
            {"branch1": activation[0], "branch2": activation[1], "trunk": activation[2], "output merger": activation[2]},
            "Glorot normal",
            regularization=["l2", 1e-6],
            merge_operation="mul",  
            trunk_last_activation=True, # add activation to the last layer of the trunk net
            layer_sizes_merger=None,
            output_merge_operation="mul",
            layer_sizes_output_merger=output_merger_layers,
        )


    def output_transform(inputs, outputs):
         return outputs**2

    net.apply_output_transform(output_transform)

    learning_rate = 1e-3
    if optimizer=="adam":
        optimizer = torch.optim.Adam(net.parameters())#, amsgrad=True)
    model = dde.Model(data, net)
    # optimizer ="rmsprop" #"rmsprop" #adamw
    if isRestore:
        model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), loss="mse", metrics=["l2 relative error"]) #loss=loss_func, 
        model.restore(model_restore_path)
        print("Model restored from: ", model_restore_path)
    else:  
        print("Batch size of branch and trunk:", (batch_b, batch_t), ", Optimizer:", optimizer, ", #Iterations:", iterations)
        model.compile(optimizer, lr=learning_rate,  decay=("inverse time", iterations // 100, 1e-3),  metrics=["l2 relative error"])
        checkpointer = dde.callbacks.ModelCheckpoint(model_save_path, save_better_only=True, period=2000)
        losshistory, train_state = model.train(iterations=iterations, batch_size= batch_b , display_every=500, callbacks=[checkpointer], model_save_path=model_save_path)
        dde.saveplot(losshistory, train_state, issave=True, isplot=False, output_dir=output_dir)
    print("# Parameters:", net.num_trainable_parameters())

    return model


def deeponet_3d_fourier_nested1(X_train, I_train, X_test, I_test, 
                     isRestore=False, isKAN=False,
                     model_restore_path = "../../model/model2d_pool_weighted-200000.pt",
                     model_save_path = "../../model/model2d_pool_100",
                     output_dir = "../../logs/history",
                     activation = ["relu", "relu", "gelu"],
                     batch_b = 2**3,
                     batch_t = 151**2,
                     n = 128,
                     m=16,
                     iterations = 300000,
                     optimizer ="adam",
                     withbc = False):  
    
    nsk =  X_train[0].shape[0]*X_train[0].shape[1]*X_train[0].shape[2]*X_train[0].shape[3]
    nst = X_train[1].shape[0]*X_train[1].shape[1]*X_train[1].shape[2]*X_train[1].shape[3]
    dim_x = X_train[-1].shape[-1]
    print("dim_x:", nsk, nst, dim_x)
    print("Activation functions: ", activation)
    for i in range(len(activation)):
        if activation[i] == "gelu":
            activation[i] = torch.nn.GELU()
        elif activation[i] == "swish":
            activation[i] = torch.nn.SiLU()

    data = QuadrupleCartesianProd(X_train, I_train, X_test, I_test)
    # (3979, 30, 30, 30, 16) (3979, 24, 24, 40, 2) (16, 2) (3979, 368640) (20, 368640)
    print(data.train_x[0].shape, data.train_x[1].shape, data.train_x[-1].shape, data.train_y.shape, data.test_y.shape)

    if isKAN:
        trunk_net = KANNet(layers_hidden=[dim_x, m, m, n],
                            grid_min = -1,
                            grid_max = 1,
                            num_grids = 50,
                            use_base_update = True,
                            use_layernorm = False,
                            base_activation = F.silu, #nn.GELU(), 
                            spline_weight_init_scale = 0.01,) #0.1
        X_trunk_layers = [None,trunk_net]
        print("trunk_net: ", trunk_net)
    else:
        X_trunk_layers = [dim_x,n,n,n,n]
    
    X_branch1_layers = [nsk,branchDeconv1(n)]
    X_branch2_layers = [nst,branch1(n)]

    output_merger_layers = [n, decoder(10,10,10,n)]

    net = MIONetCartesianProd(
            X_branch1_layers,
            X_branch2_layers,
            X_trunk_layers,
            {"branch1": activation[0], "branch2": activation[1], "trunk": activation[2], "output merger": activation[2]},
            "Glorot normal",
            regularization=["l2", 2e-6],
            merge_operation="mul",  
            trunk_last_activation=True, # add activation to the last layer of the trunk net
            layer_sizes_merger=None,
            output_merge_operation="mul",
            layer_sizes_output_merger=output_merger_layers,
        )


    def output_transform(inputs, outputs):
         return outputs**2

    net.apply_output_transform(output_transform)

    learning_rate = 1e-3
    if optimizer=="adam":
        optimizer = torch.optim.Adam(net.parameters())#, amsgrad=True)
    model = dde.Model(data, net)
    # optimizer ="rmsprop" #"rmsprop" #adamw
    if isRestore:
        model.compile(optimizer, lr=learning_rate, decay=("inverse time", iterations // 100, 1e-3), loss="mse", metrics=["l2 relative error"]) #loss=loss_func, 
        model.restore(model_restore_path)
        print("Model restored from: ", model_restore_path)
    else:  
        print("Batch size of branch and trunk:", (batch_b, batch_t), ", Optimizer:", optimizer, ", #Iterations:", iterations)
        model.compile(optimizer, lr=learning_rate,  decay=("inverse time", iterations // 100, 1e-3),  metrics=["l2 relative error"])
        checkpointer = dde.callbacks.ModelCheckpoint(model_save_path, save_better_only=True, period=2000)
        losshistory, train_state = model.train(iterations=iterations, batch_size= batch_b , display_every=100, callbacks=[checkpointer], model_save_path=model_save_path)
        dde.saveplot(losshistory, train_state, issave=True, isplot=False, output_dir=output_dir)
    print("# Parameters:", net.num_trainable_parameters())

    return model