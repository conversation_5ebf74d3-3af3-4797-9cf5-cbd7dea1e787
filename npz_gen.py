import os
import numpy as np
import matplotlib.pyplot as plt

def format_time(t):
    t = float(f"{t:.2f}")
    if t % 1 == 0:
        return f"{int(t)}"
    elif t * 10 % 1 == 0:
        return f"{t:.1f}"
    else:
        return f"{t:.2f}"

def plot_3d(x, y, z, c_val):
    fig = plt.figure()
    ax = plt.axes(projection='3d')
    ax.set_aspect('auto')
    p = ax.scatter(x, y, z, c=c_val, s=0.05)
    fig.colorbar(p, pad=0.1)
    ax.set_xlabel("$z$")
    ax.set_ylabel("$x$")
    ax.set_zlabel("$y$")
    plt.show()

def plot_2d(x, y, z, c_val, slice_val):
    mask = np.isclose(y, slice_val)
    plt.figure()
    plt.scatter(x[mask], z[mask], c=c_val[mask], s=0.1)
    plt.xlim(-1.5, 1.5)
    plt.ylim(0, 3)
    plt.xlabel("$x$")
    plt.ylabel("$z$")
    plt.title(f"2D Plot for y={slice_val}")
    plt.show()

def process_npz_for_deeponet(npz_filename, out_filename, isdebug=False):
    data = np.load(npz_filename, allow_pickle=True)
    X = data["x"]
    Y = data["y"]
    Z = data["z"]
    
    numeric_keys = data["numeric_keys"].tolist()
    numeric_keys.sort(key=float)
    N = len(numeric_keys)
    
    # Assume each key's T is shape (nx,)
    sample_T = data[f"T_{numeric_keys[0]}"]
    nx = sample_T.shape[0]
    
    # Preallocate arrays for N snapshots
    T_all = np.zeros((N, nx), dtype=np.float32)
    kappa_all = np.zeros((N, nx), dtype=np.float32)
    G_all = np.zeros((N, nx), dtype=np.float32)

    I_all = np.zeros((N, nx, 16), dtype=np.float32)
    
    for i, key in enumerate(numeric_keys):
        T_all[i, :] = data[f"T_{key}"]
        G_all[i, :] = data[f"G_{key}"]
        kappa_all[i, :] = data[f"kappa_{key}"]
        I_all[i, :, :] = data[f"I_{key}"].T
    
    # Create trunk coordinates (assume x, y, z are already the flattened spatial grid of length nx)
    coords = np.column_stack((X, Y, Z)).astype(np.float32)  # shape (nx, 3)
    
    # Compute angular information (nTheta=2, nPhi=2 => ns=16)
    nTheta, nPhi = 2, 2
    deltaPhi = np.pi / (2.0 * nPhi)
    deltaTheta = np.pi / nTheta
    ts = [(2*n + 1)*deltaTheta/2.0 for n in range(nTheta)]
    ps = [(2*m + 1)*deltaPhi/2.0 for m in range(4*nPhi)]
    ps, ts = np.meshgrid(ps, ts)
    ps, ts = ps.flatten(), ts.flatten()
    
    # Build trunk: tile spatial coordinates for each of the ns (16) directions and append angular info
    X_trunk = np.hstack((
        np.tile(X.reshape((-1, 1)), (16, 1)),
        np.tile(Y.reshape((-1, 1)), (16, 1)),
        np.tile(Z.reshape((-1, 1)), (16, 1)),
        np.repeat(ps, nx).reshape((-1, 1)),
        np.repeat(ts, nx).reshape((-1, 1))
    )).astype(np.float32)
    
    if isdebug:
        plot_3d(X, Y, Z, T_all[0, :])
        plot_2d(X, Y, Z, T_all[0, :], slice_val=1.45)
        # plot_3d(X_trunk[:nx, 0], X_trunk[:nx, 1], X_trunk[:nx, 2], I_all[0][0, :])
    
    print("X_branch1 shape:", kappa_all.shape)
    print("X_branch2 shape:", T_all.shape)
    print("X_trunk shape:", X_trunk.shape)
    I_final = I_all.reshape(N, -1)
    print("I shape:", I_final.shape)
    
    np.savez_compressed(out_filename,
                        X_branch1=kappa_all,
                        X_branch2=T_all,
                        X_trunk=X_trunk,  # (nx,5)
                        I=I_final,
                        numeric_keys=numeric_keys)
    print(f"Processed dataset saved to {out_filename}")

if __name__ == "__main__":
    npz_file = "/Users/<USER>/Downloads/data/data/firefoam_data_19_156.724_160.npz"
    out_file = "/Users/<USER>/Downloads/data/firefoam_data_last_160.npz"
    process_npz_for_deeponet(npz_file, out_file, isdebug=True)