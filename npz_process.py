import os
import h5py
import numpy as np

def split_h5_to_two_npz(h5_filename, out_dir):
    """
    Split a single .h5 file into two .npz files by dividing numeric_keys into two halves.
    The first .npz file contains the first half of numeric_keys,
    and the second .npz file contains the second half.
    Each output file is named with the start and end key for clarity.
    """
    base_name = os.path.splitext(os.path.basename(h5_filename))[0]  # e.g. "firefoam_data_0"
    with h5py.File(h5_filename, 'r') as f:
        all_keys = list(f.keys())
        # Exclude x,y,z
        numeric_keys = [k for k in all_keys if k not in ["x", "y", "z"]]
        numeric_keys.sort(key=float)
        
        # Read global coords
        x = f["x"][()]
        y = f["y"][()]
        z = f["z"][()]

        # If there's fewer than 2 numeric keys, skip splitting
        if len(numeric_keys) < 2:
            print(f"Not enough numeric keys in {h5_filename} to split. Saving as single npz.")
            data_dict = {
                "x": x,
                "y": y,
                "z": z,
                "numeric_keys": numeric_keys
            }
            for key in numeric_keys:
                grp = f[key]
                # Check T, G, aLambda_0
                if ("T" in grp and "G" in grp and "aLambda_0" in grp):
                    T = grp["T"][()]
                    G = grp["G"][()]
                    kappa = grp["aLambda_0"][()]
                    # Collect ILambda_ keys
                    I_keys = [ik for ik in grp.keys() if ik.startswith("ILambda_")]
                    I_keys.sort(key=lambda s: int(s.split("_")[1]))
                    I_array = np.array([grp[ik][()] for ik in I_keys])
                    
                    data_dict[f"T_{key}"] = T
                    data_dict[f"G_{key}"] = G
                    data_dict[f"kappa_{key}"] = kappa
                    data_dict[f"I_{key}"] = I_array
            single_npz = os.path.join(out_dir, base_name + "_single.npz")
            np.savez_compressed(single_npz, **data_dict)
            print(f"Saved single .npz => {single_npz}")
            return

        mid_index = len(numeric_keys) // 2
        keys_first_half = numeric_keys[:mid_index]
        keys_second_half = numeric_keys[mid_index:]

        # Prepare two dicts
        dataA = {
            "x": x,
            "y": y,
            "z": z,
            "numeric_keys": keys_first_half
        }
        dataB = {
            "x": x,
            "y": y,
            "z": z,
            "numeric_keys": keys_second_half
        }

        # Fill dataA
        for key in keys_first_half:
            grp = f[key]
            if ("T" in grp and "G" in grp and "aLambda_0" in grp):
                T = grp["T"][()]
                G = grp["G"][()]
                kappa = grp["aLambda_0"][()]
                I_keys = [ik for ik in grp.keys() if ik.startswith("ILambda_")]
                I_keys.sort(key=lambda s: int(s.split("_")[1]))
                if len(I_keys) == 0:
                    print(f"Warning: {key} has no ILambda_*, skip.")
                    continue
                I_array = np.array([grp[ik][()] for ik in I_keys])
                
                dataA[f"T_{key}"] = T
                dataA[f"G_{key}"] = G
                dataA[f"kappa_{key}"] = kappa
                dataA[f"I_{key}"] = I_array

        # Fill dataB
        for key in keys_second_half:
            grp = f[key]
            if ("T" in grp and "G" in grp and "aLambda_0" in grp):
                T = grp["T"][()]
                G = grp["G"][()]
                kappa = grp["aLambda_0"][()]
                I_keys = [ik for ik in grp.keys() if ik.startswith("ILambda_")]
                I_keys.sort(key=lambda s: int(s.split("_")[1]))
                if len(I_keys) == 0:
                    print(f"Warning: {key} has no ILambda_*, skip.")
                    continue
                I_array = np.array([grp[ik][()] for ik in I_keys])
                
                dataB[f"T_{key}"] = T
                dataB[f"G_{key}"] = G
                dataB[f"kappa_{key}"] = kappa
                dataB[f"I_{key}"] = I_array

        # Name them using the start & end key
        # For part A: from numeric_keys[0] to numeric_keys[mid_index - 1]
        # For part B: from numeric_keys[mid_index] to numeric_keys[-1]
        startA = keys_first_half[0]
        endA = keys_first_half[-1]
        startB = keys_second_half[0]
        endB = keys_second_half[-1]

        outA = os.path.join(out_dir, f"{base_name}_{startA}_{endA}.npz")
        outB = os.path.join(out_dir, f"{base_name}_{startB}_{endB}.npz")

        # Save partA if it has any keys
        if len(keys_first_half) > 0:
            np.savez_compressed(outA, **dataA)
            print(f"Saved {outA} with {len(keys_first_half)} numeric keys.")
        # Save partB if it has any keys
        if len(keys_second_half) > 0:
            np.savez_compressed(outB, **dataB)
            print(f"Saved {outB} with {len(keys_second_half)} numeric keys.")

if __name__ == "__main__":
    import os

    out_dir = "/Users/<USER>/Downloads/data/data/"
    # os.makedirs(out_dir, exist_ok=True)
    # split_h5_to_two_npz("firefoam_data_15.h5", out_dir)


    for i in range(16,20):
        h5_file = f"/Users/<USER>/Downloads/data/data/firefoam_data_{i}.h5"
        split_h5_to_two_npz(h5_file, out_dir)

