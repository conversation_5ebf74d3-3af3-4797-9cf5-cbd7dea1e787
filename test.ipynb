{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b2b551a7", "metadata": {}, "outputs": [], "source": ["DATA_DIR = \"/Users/<USER>/Downloads/data/data\"\n", "FILE_PATTERN = \"lfour_data_*.npz\"\n", "\n", "import os\n", "import glob\n", "import re\n", "import numpy as np\n", " \n", "def main(DATA_DIR, FILE_PATTERN):\n", "    # 使用 glob 得到所有匹配文件\n", "    file_list = glob.glob(os.path.join(DATA_DIR, FILE_PATTERN))\n", "    file_list.sort()\n", "    \n", "    # 使用正则表达式筛选数值在[10, 60]之间的文件\n", "    pattern = re.compile(r\"lfour_data_(\\d+(?:\\.\\d+)?).npz\")\n", "    valid_files = []\n", "    for f in file_list:\n", "        fname = os.path.basename(f)\n", "        match = pattern.match(fname)\n", "        if match:\n", "            val = float(match.group(1))\n", "            if 10 <= val <= 60:\n", "                valid_files.append(f)\n", "    print(\"Valid files:\", valid_files)"]}, {"cell_type": "code", "execution_count": 2, "id": "3f98f18f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valid files: ['/Users/<USER>/Downloads/data/data/lfour_data_12.npz']\n"]}], "source": ["main(DATA_DIR, FILE_PATTERN)"]}, {"cell_type": "code", "execution_count": 2, "id": "b1eb1e8d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Data\n", "steps = np.array([0, 500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, \n", "                 5500, 6000, 6500, 7000, 7500, 8000, 8500, 9000, 9500, 10000])\n", "train_loss = np.array([4.09e-02, 3.60e-04, 2.46e-04, 1.55e-04, 1.46e-04, 9.90e-05, \n", "                      7.84e-05, 5.98e-05, 5.06e-05, 4.99e-05, 4.01e-05, 2.99e-05, \n", "                      2.06e-05, 2.03e-05, 1.73e-05, 1.57e-05, 1.47e-05, 2.17e-05, \n", "                      5.46e-05, 9.53e-06, 9.78e-06])\n", "test_loss = np.array([3.97e-02, 3.85e-04, 2.29e-04, 2.55e-04, 2.70e-04, 3.15e-04, \n", "                     3.45e-04, 3.54e-04, 4.42e-04, 3.73e-04, 4.80e-04, 4.24e-04, \n", "                     4.35e-04, 4.26e-04, 4.37e-04, 4.51e-04, 4.46e-04, 4.27e-04, \n", "                     5.96e-04, 4.69e-04, 4.61e-04])\n", "test_metric = np.array([9.97e-01, 9.81e-02, 7.57e-02, 7.99e-02, 8.22e-02, 8.87e-02, \n", "                       9.29e-02, 9.41e-02, 1.05e-01, 9.66e-02, 1.10e-01, 1.03e-01, \n", "                       1.04e-01, 1.03e-01, 1.05e-01, 1.06e-01, 1.06e-01, 1.03e-01, \n", "                       1.22e-01, 1.08e-01, 1.07e-01])\n", "\n", "# Create figure\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Plot all metrics on the same plot\n", "plt.semilogy(steps, train_loss, 'b-', label='Train loss', linewidth=2)\n", "plt.semilogy(steps, test_loss, 'r-', label='Test loss', linewidth=2)\n", "plt.semilogy(steps, test_metric, 'g-', label='Test metric', linewidth=2)\n", "\n", "# Customize the plot\n", "plt.xlabel('Steps')\n", "plt.ylabel('Value')\n", "plt.title('Training Metrics')\n", "plt.grid(True, which=\"both\", ls=\"-\", alpha=0.2)\n", "plt.legend(loc='upper right')\n", "\n", "# Adjust layout and show plot\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "torchen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}