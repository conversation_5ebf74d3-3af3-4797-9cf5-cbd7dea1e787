import os
import numpy as np
from scipy.interpolate import griddata

def process_and_sample_npz_lone(npz_filename, out_filename, isdebug=False):
    data = np.load(npz_filename, allow_pickle=True)
    X, Y, Z = np.loadtxt('/gpfs/gibbs/project/lu_lu/wj259/RTEDeepONet/meshCellCenters.txt', usecols=(0, 1, 2), unpack=True)

    numeric_keys = [str(k) for k in data["numeric_keys"].tolist()]
    numeric_keys.sort(key=lambda s: float(s))
    valid_keys = [k for k in numeric_keys if all(f in data for f in (f"T_{k}", f"G_{k}", f"kappa_{k}", f"I_{k}"))]

    N = len(valid_keys)
    nx = data[f"T_{valid_keys[0]}"].shape[0]

    T_all = np.array([data[f"T_{k}"] for k in valid_keys], dtype=np.float32)
    G_all = np.array([data[f"G_{k}"] for k in valid_keys], dtype=np.float32)
    kappa_all = np.array([data[f"kappa_{k}"] for k in valid_keys], dtype=np.float32)
    I_all = np.zeros((N, nx, 16), dtype=np.float32)
    for i, k in enumerate(valid_keys):
        I_raw = data[f"I_{k}"]
        I_all[i] = I_raw.T if I_raw.shape[0] == 16 else I_raw

    nTheta, nPhi = 2, 2
    deltaPhi = np.pi / (2 * nPhi)
    deltaTheta = np.pi / nTheta
    ts = [(2*n + 1) * deltaTheta / 2.0 for n in range(nTheta)]
    ps = [(2*m + 1) * deltaPhi / 2.0 for m in range(4 * nPhi)]
    ps, ts = np.meshgrid(ps, ts)
    ps, ts = ps.flatten(), ts.flatten()

    X_trunk = np.hstack((
        np.tile(X.reshape(-1, 1), (16, 1)),
        np.tile(Y.reshape(-1, 1), (16, 1)),
        np.tile(Z.reshape(-1, 1), (16, 1)),
        np.repeat(ps, nx).reshape(-1, 1),
        np.repeat(ts, nx).reshape(-1, 1)
    )).astype(np.float32)

    X_full, Y_full, Z_full = X_trunk[:, 0], X_trunk[:, 1], X_trunk[:, 2]

    lone_mask_intp = (
        (X_full >= -0.3) & (X_full <= 0.3) &
        (Y_full >= -0.3) & (Y_full <= 0.3) &
        (Z_full >= 0.0) & (Z_full <= 1.0)
    )
    nx1 = int(lone_mask_intp.shape[0] / 16)

    grid_x, grid_y, grid_z = np.mgrid[
        -0.285:0.285:48j,
        -0.285:0.285:48j,
        0.025:0.975:80j
    ]

    def interpolate_to_level(X, Y, Z, grid_x, grid_y, grid_z, values, mask):
        points = np.vstack((X[mask], Y[mask], Z[mask])).T
        values = values[mask]
        return griddata(points, values, (grid_x, grid_y, grid_z), method='nearest').flatten()

    X_branch1_intp1 = np.zeros((N, 48 * 48 * 80))
    X_branch2_intp1 = np.zeros((N, 48 * 48 * 80))
    Is_intp1 = np.zeros((N, 16 * 48 * 48 * 80))

    for i in range(N):
        X_branch1_intp1[i] = interpolate_to_level(X_full[:nx1], Y_full[:nx1], Z_full[:nx1], grid_x, grid_y, grid_z, kappa_all[i], lone_mask_intp[:nx1])
        X_branch2_intp1[i] = interpolate_to_level(X_full[:nx1], Y_full[:nx1], Z_full[:nx1], grid_x, grid_y, grid_z, T_all[i], lone_mask_intp[:nx1])
        for j in range(16):
            sl = slice(j * nx1, (j + 1) * nx1)
            Is_intp1[i, j * 48**2 * 80:(j + 1) * 48**2 * 80] = interpolate_to_level(
                X_full[sl], Y_full[sl], Z_full[sl], grid_x, grid_y, grid_z, I_all[i, :, j], lone_mask_intp[sl]
            )

    ns = nTheta * nPhi * 4
    ps, ts = np.meshgrid(
        [(2*m + 1)*np.pi/(4*nPhi) for m in range(4*nPhi)],
        [(2*n + 1)*np.pi/(2*nTheta) for n in range(nTheta)]
    )
    ps, ts = ps.flatten(), ts.flatten()

    X_trunk_intp1 = np.hstack((
        np.tile(grid_x.reshape((-1, 1)), (ns, 1)),
        np.tile(grid_y.reshape((-1, 1)), (ns, 1)),
        np.tile(grid_z.reshape((-1, 1)), (ns, 1)),
        np.repeat(ps, 48*48*80).reshape((-1, 1)),
        np.repeat(ts, 48*48*80).reshape((-1, 1))
    )).astype(np.float32)

    np.savez_compressed(out_filename,
                        X_branch1=X_branch1_intp1,
                        X_branch2=X_branch2_intp1,
                        X_trunk=X_trunk_intp1,
                        I=Is_intp1,
                        numeric_keys=valid_keys)
    print(f"Saved: {out_filename}")


if __name__ == "__main__":
    src_dir = "/gpfs/gibbs/project/lu_lu/wj259/RTEDeepONet/data"
    tgt_dir = "/gpfs/gibbs/project/lu_lu/wj259/RTEDeepONet/data_lone"
    os.makedirs(tgt_dir, exist_ok=True)

    npz_files = sorted(f for f in os.listdir(src_dir) if f.endswith(".npz"))

    for fname in npz_files:
        t0 = fname.split("_")[3]
        src_path = os.path.join(src_dir, fname)
        out_name = f"lone_data_{t0}.npz"
        out_path = os.path.join(tgt_dir, out_name)
        process_and_sample_npz_lone(src_path, out_path)
        print(f"Processed {fname} → {out_name}")